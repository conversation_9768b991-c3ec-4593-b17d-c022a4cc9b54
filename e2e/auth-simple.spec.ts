import { test, expect } from '@playwright/test';

test('should navigate to signin page', async ({ page }) => {
  // Navigate to signin page
  await page.goto('/auth/signin');
  
  // Log the current URL
  console.log('Current URL:', page.url());
  
  // Check if we're on the signin page
  expect(page.url()).toContain('/auth/signin');
  
  // Look for common signin elements
  const emailInput = page.locator('input[name="email"], input[type="email"], input#email');
  const passwordInput = page.locator('input[name="password"], input[type="password"], input#password');
  
  // Wait for inputs to be visible
  await expect(emailInput).toBeVisible({ timeout: 5000 });
  await expect(passwordInput).toBeVisible({ timeout: 5000 });
  
  console.log('Found email and password inputs');
});

test('should attempt login', async ({ page }) => {
  await page.goto('/auth/signin');
  
  // Find and fill email
  const emailInput = page.locator('input[name="email"], input[type="email"], input#email').first();
  await emailInput.fill('<EMAIL>');
  
  // Find and fill password
  const passwordInput = page.locator('input[name="password"], input[type="password"], input#password').first();
  await passwordInput.fill('testuser123');
  
  // Find and click submit button
  const submitButton = page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first();
  await submitButton.click();
  
  // Wait for navigation or error
  await page.waitForLoadState('networkidle', { timeout: 5000 }).catch(() => {});
  
  console.log('After login attempt, URL:', page.url());
  
  // Check if we navigated away from signin
  expect(page.url()).not.toContain('/auth/signin');
});