#!/bin/bash
# Service-Specific Health Check Script
# Usage: ./service-health-check.sh <service-name>
# Returns 0 if healthy, 1 if unhealthy

set -euo pipefail

SERVICE="${1:-all}"

# Function to check a specific service
check_service() {
    local service=$1
    local status=0
    
    case "$service" in
        "backend")
            if curl -s -f "http://localhost:5001/health" >/dev/null 2>&1; then
                # Check database connection
                DB_STATUS=$(curl -s "http://localhost:5001/health" | grep -o '"database":"[^"]*"' | cut -d'"' -f4 || echo "unknown")
                if [ "$DB_STATUS" = "healthy" ]; then
                    echo "Backend: healthy"
                else
                    echo "Backend: unhealthy (database: $DB_STATUS)"
                    status=1
                fi
            else
                echo "Backend: unhealthy (not responding)"
                status=1
            fi
            ;;
            
        "ml")
            if curl -s -f "http://localhost:5002/health" >/dev/null 2>&1; then
                # Check model status
                MODEL_STATUS=$(curl -s "http://localhost:5002/model/status" 2>/dev/null | grep -o '"loaded":[^,}]*' | cut -d':' -f2 || echo "false")
                if [ "$MODEL_STATUS" = "true" ]; then
                    echo "ML Service: healthy"
                else
                    echo "ML Service: degraded (model not loaded)"
                    status=1
                fi
            else
                echo "ML Service: unhealthy (not responding)"
                status=1
            fi
            ;;
            
        "database")
            if docker exec spheroseg-db pg_isready -U spheroseg_prod -d spheroseg >/dev/null 2>&1; then
                echo "Database: healthy"
            else
                echo "Database: unhealthy"
                status=1
            fi
            ;;
            
        "redis")
            if docker exec spheroseg-redis redis-cli ping | grep -q "PONG" 2>/dev/null; then
                echo "Redis: healthy"
            else
                echo "Redis: unhealthy"
                status=1
            fi
            ;;
            
        "rabbitmq")
            if docker exec spheroseg-rabbitmq rabbitmq-diagnostics ping >/dev/null 2>&1; then
                echo "RabbitMQ: healthy"
            else
                echo "RabbitMQ: unhealthy"
                status=1
            fi
            ;;
            
        "frontend")
            if curl -s -f "http://localhost/" -o /dev/null 2>&1; then
                echo "Frontend: healthy"
            else
                echo "Frontend: unhealthy"
                status=1
            fi
            ;;
            
        "nginx")
            if docker exec spheroseg-nginx-prod nginx -t >/dev/null 2>&1; then
                echo "Nginx: healthy"
            else
                echo "Nginx: unhealthy"
                status=1
            fi
            ;;
            
        *)
            echo "Unknown service: $service"
            status=1
            ;;
    esac
    
    return $status
}

# Main logic
if [ "$SERVICE" = "all" ]; then
    SERVICES=("backend" "ml" "database" "redis" "rabbitmq" "frontend" "nginx")
    OVERALL_STATUS=0
    
    for svc in "${SERVICES[@]}"; do
        if ! check_service "$svc"; then
            OVERALL_STATUS=1
        fi
    done
    
    exit $OVERALL_STATUS
else
    check_service "$SERVICE"
fi