# Secrets Management for SpherosegV4 Production

## Overview

This document describes the secrets management system implemented for SpherosegV4 production deployment. The system provides automated validation and rotation of critical security credentials.

## Scripts Location

All secrets management scripts are located in:
```
/home/<USER>/spheroseg/scripts/secrets-management/
```

## Available Scripts

### 1. validate-secrets.sh

Validates all required secrets in the production environment file.

**Usage:**
```bash
./scripts/secrets-management/validate-secrets.sh [env-file]
```

**Default environment file:** `/home/<USER>/spheroseg/.env.production.secure`

**What it validates:**
- JWT secrets (minimum 64 characters, Base64 encoded)
- Session and CSRF secrets (minimum 32 characters)
- Database passwords (minimum 16 characters)
- RabbitMQ credentials (minimum 16 characters)
- Redis password (minimum 16 characters)
- Email credentials (if email notifications are enabled)
- SSL/Security configuration flags

**Example output:**
```
✓ JWT_SECRET - JWT signing secret (length: 88)
✓ JWT_REFRESH_SECRET - JWT refresh token secret (length: 88)
✓ All secrets validated successfully
```

### 2. rotate-secrets.sh

Automatically rotates critical secrets with proper backup.

**Usage:**
```bash
./scripts/secrets-management/rotate-secrets.sh
```

**What it rotates automatically:**
- JWT_SECRET (64 bytes)
- JWT_REFRESH_SECRET (64 bytes)
- SESSION_SECRET (48 bytes)
- CSRF_SECRET (48 bytes)

**What requires manual rotation:**
- Database passwords (requires PostgreSQL user update)
- RabbitMQ credentials (requires RabbitMQ user update)
- Redis password (requires Redis configuration update)

**Backup location:** `/home/<USER>/spheroseg/secrets/backups/[timestamp]/`

## Security Best Practices

### 1. Regular Rotation Schedule

- **JWT secrets:** Rotate every 90 days
- **Session/CSRF secrets:** Rotate every 90 days
- **Database passwords:** Rotate every 180 days
- **Service passwords:** Rotate every 180 days

### 2. Rotation Process

#### Automated Rotation (JWT/Session/CSRF)
```bash
# 1. Run rotation script
./scripts/secrets-management/rotate-secrets.sh

# 2. Restart affected services
docker-compose --env-file .env.production.secure --profile prod restart backend

# 3. Verify services are running
docker-compose --profile prod ps
```

#### Manual Database Password Rotation
```bash
# 1. Generate new password
openssl rand -base64 32

# 2. Connect to database
docker-compose exec db psql -U postgres

# 3. Update user password
ALTER USER spheroseg_prod WITH PASSWORD 'new_password_here';

# 4. Update environment file
# Edit DATABASE_URL and DB_PASSWORD in .env.production.secure

# 5. Restart backend service
docker-compose --env-file .env.production.secure --profile prod restart backend
```

#### Manual RabbitMQ Password Rotation
```bash
# 1. Generate new password
openssl rand -base64 24

# 2. Access RabbitMQ container
docker-compose exec rabbitmq sh

# 3. Change password
rabbitmqctl change_password spheroseg_user 'new_password_here'

# 4. Update environment file
# Edit RABBITMQ_PASS in .env.production.secure

# 5. Restart affected services
docker-compose --env-file .env.production.secure --profile prod restart backend ml
```

### 3. Emergency Procedures

#### If rotation causes service failure:
1. Check backup directory for previous secrets
2. Restore from backup: `cp secrets/backups/[timestamp]/secrets.backup .env.production.secure.restored`
3. Compare and manually restore working secrets
4. Restart services with restored configuration

#### If secrets are compromised:
1. Immediately rotate ALL secrets using the rotation script
2. Manually rotate database and service passwords
3. Review access logs for unauthorized access
4. Notify security team if applicable

### 4. Automation with Cron

Add to root crontab for automated validation:
```bash
# Daily secrets validation at 2 AM
0 2 * * * /home/<USER>/spheroseg/scripts/secrets-management/validate-secrets.sh >> /var/log/spheroseg-secrets.log 2>&1

# Quarterly secrets rotation (1st day of Jan, Apr, Jul, Oct)
0 3 1 1,4,7,10 * /home/<USER>/spheroseg/scripts/secrets-management/rotate-secrets.sh >> /var/log/spheroseg-rotation.log 2>&1
```

## Monitoring and Alerts

### Log Files
- Validation logs: `/var/log/spheroseg-secrets.log`
- Rotation logs: `/var/log/spheroseg-rotation.log`
- Backup files: `/home/<USER>/spheroseg/secrets/backups/*/`

### Integration with Monitoring
The validation script returns exit codes:
- 0: All secrets valid
- 1: Validation errors found

This can be integrated with monitoring systems to alert on validation failures.

## Security Considerations

1. **File Permissions**: Ensure `.env.production.secure` has restricted permissions (600)
2. **Backup Security**: Backup directories should also have restricted access
3. **Audit Trail**: All rotations are logged with timestamps
4. **No Plain Text**: Never store passwords in plain text or commit to version control
5. **Secure Generation**: Always use cryptographically secure random generators

## Troubleshooting

### Common Issues

1. **"Permission denied" when running scripts**
   ```bash
   chmod +x scripts/secrets-management/*.sh
   ```

2. **"Secret not found" errors**
   - Check if the secret exists in the environment file
   - Run validation script to identify missing secrets

3. **Services fail after rotation**
   - Check docker logs: `docker-compose logs [service]`
   - Verify new secrets are properly formatted
   - Restore from backup if needed

4. **Database connection failures after password rotation**
   - Ensure PostgreSQL user password was updated
   - Verify DATABASE_URL includes new password
   - Check for special characters that need escaping

## Future Enhancements

1. **HashiCorp Vault Integration**: For centralized secrets management
2. **AWS Secrets Manager**: For cloud-based rotation
3. **Kubernetes Secrets**: When migrating to K8s
4. **Automated Database Password Rotation**: Script PostgreSQL password updates
5. **Secret Encryption at Rest**: Additional layer of protection