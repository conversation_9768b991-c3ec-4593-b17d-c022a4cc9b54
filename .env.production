# Production Environment Variables for SpherosegV4
# Generated: 2025-01-23
# IMPORTANT: This file contains sensitive information. Keep it secure\!

NODE_ENV=production
APP_NAME=SpherosegV4
APP_URL=https://spherosegapp.utia.cas.cz

# Backend API
API_PORT=5001
API_URL=http://localhost:5001

# Database (MUST CHANGE DEFAULT CREDENTIALS\!)
DATABASE_URL=**************************************/spheroseg
DB_SSL=false
DB_PASSWORD=postgres
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=spheroseg

# Redis (without password for local testing)
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=

# RabbitMQ (default credentials for local testing)
RABBITMQ_DEFAULT_USER=rabbitmq
RABBITMQ_DEFAULT_PASS=guest
RABBITMQ_USER=rabbitmq
RABBITMQ_PASS=guest
RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672
RABBITMQ_QUEUE=segmentation_tasks

# JWT & Security (cryptographically secure secrets)
JWT_SECRET=76QAHCbMm9dKkM1nR77FA3UoqJyHrVAdkY44vfhzxd1MFATXGFLGAp/qrYRGaW4qT0KDeLeqrQn59s9m6nEebg==
JWT_REFRESH_SECRET=byVLZr4wsyHt4uRl5vrXMzezCrV+2GNhn/NF3DGm+CX9PN2VvQISxG1t1HZ0T745AYKi2pV0q6fCdUs+0Em0kw==
SESSION_SECRET=ePmn4S22siByCL3fm5pdasrnHj/bQcUU7rTOb3RtJwUwz1RF2e3GUtFVzrPlQWIp
CSRF_SECRET=vc0vAQxhVZ15luZgUH1RIkl6h1ASOhs9q6N4oIFmO8NHBGQ6rop1rvfNSYpaYE+u

# Email Configuration
EMAIL_FROM=<EMAIL>
EMAIL_HOST=mail.utia.cas.cz
EMAIL_PORT=25
EMAIL_USER=<EMAIL>
EMAIL_PASS=CHANGE_THIS_EMAIL_PASSWORD

# CORS (production domains only)
ALLOWED_ORIGINS=https://spherosegapp.utia.cas.cz

# SSL/HTTPS
SECURE_COOKIES=true
FORCE_HTTPS=true
REQUIRE_HTTPS=true
HSTS_MAX_AGE=31536000

# Performance & Monitoring
ENABLE_PERFORMANCE_MONITORING=true
LOG_LEVEL=info
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
ENABLE_RATE_LIMIT=true
CONTAINER_MEMORY_LIMIT_MB=1024

# Security Features
ENABLE_SECURITY_HEADERS=true
CSP_REPORT_URI=https://spherosegapp.utia.cas.cz/api/csp-report
ENABLE_CSRF_PROTECTION=true

# Monitoring & Logging
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
ELASTIC_PORT=9200
KIBANA_PORT=5601

# Backup Configuration
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=spheroseg-backups
BACKUP_S3_REGION=eu-west-1

# Feature Flags
ENABLE_ML_SERVICE=true
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_WEBSOCKET=true
ENABLE_EXPORT_FEATURES=true

# Frontend Configuration
VITE_API_URL=https://spherosegapp.utia.cas.cz
VITE_API_BASE_URL=/api
VITE_ASSETS_URL=https://spherosegapp.utia.cas.cz/assets
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_TRACKING=true
