#!/usr/bin/env node

import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from .env.production.secure
const envPath = path.join(__dirname, '..', '.env.production.secure');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  envContent.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      const value = valueParts.join('=').replace(/^["'](.*)["']$/, '$1');
      process.env[key] = value;
    }
  });
}

const dbUser = process.env.POSTGRES_USER || 'spheroseg_prod';
const dbPassword = process.env.POSTGRES_PASSWORD || 'jK9mN3pQ7rS2xV5wY8zA';
const dbName = process.env.POSTGRES_DB || 'spheroseg_prod';

console.log('Initializing production database...');
console.log(`Database: ${dbName}`);
console.log(`User: ${dbUser}`);

// First, create the database if it doesn't exist
const createDbCommand = `docker-compose exec -T db psql -U postgres -c "CREATE DATABASE ${dbName};" 2>/dev/null || true`;
exec(createDbCommand, (error, stdout, stderr) => {
  if (error && !stderr.includes('already exists')) {
    console.error('Error creating database:', error);
    process.exit(1);
  }
  
  console.log('Database created or already exists');
  
  // Grant privileges to the user
  const grantCommand = `docker-compose exec -T db psql -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE ${dbName} TO ${dbUser};" 2>/dev/null || true`;
  exec(grantCommand, (error, stdout, stderr) => {
    if (error) {
      console.error('Error granting privileges:', error);
    }
    
    // Execute the schema SQL file
    const schemaPath = path.join(__dirname, '..', 'packages', 'backend', 'src', 'db', 'schema.sql');
    if (!fs.existsSync(schemaPath)) {
      console.error('Schema file not found:', schemaPath);
      process.exit(1);
    }
    
    const schemaCommand = `docker-compose exec -T db psql -U ${dbUser} -d ${dbName} < ${schemaPath}`;
    exec(schemaCommand, (error, stdout, stderr) => {
      if (error) {
        console.error('Error executing schema:', error);
        console.error('stderr:', stderr);
        process.exit(1);
      }
      
      console.log('Database schema initialized successfully');
      
      // Create test user if in development
      if (process.env.NODE_ENV !== 'production') {
        console.log('Creating test user...');
        const testUserCommand = `docker-compose exec backend npm run db:create-test-user`;
        exec(testUserCommand, (error, stdout, stderr) => {
          if (error) {
            console.error('Error creating test user:', error);
          } else {
            console.log('Test user created successfully');
          }
        });
      }
    });
  });
});