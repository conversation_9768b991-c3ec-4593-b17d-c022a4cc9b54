#!/bin/bash
# Docker cleanup script - removes old containers, images, volumes, and build cache

echo "🧹 Starting Docker cleanup..."

# Stop all containers
echo "📦 Stopping all containers..."
docker stop $(docker ps -aq) 2>/dev/null || true

# Remove all stopped containers
echo "🗑️  Removing stopped containers..."
docker container prune -f

# Remove dangling images
echo "🖼️  Removing dangling images..."
docker image prune -f

# Remove unused volumes
echo "💾 Removing unused volumes..."
docker volume prune -f

# Remove build cache
echo "🏗️  Removing build cache..."
docker builder prune -f

# Remove unused networks
echo "🌐 Removing unused networks..."
docker network prune -f

# Show disk usage
echo ""
echo "💽 Current Docker disk usage:"
docker system df

echo ""
echo "✅ Docker cleanup complete!"