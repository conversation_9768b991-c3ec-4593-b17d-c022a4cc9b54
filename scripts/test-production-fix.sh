#!/bin/bash

# Test production fix for spherosegapp.utia.cas.cz
# This script verifies that the JavaScript initialization error has been fixed

set -e

echo "🔍 Testing Production Fix for spherosegapp.utia.cas.cz"
echo "=================================================="

# Color codes
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# Function to check test result
check_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓ $2${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}✗ $2${NC}"
        ((TESTS_FAILED++))
    fi
}

# 1. Check if frontend is accessible
echo -e "\n📋 Test 1: Frontend Accessibility"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000)
if [ "$HTTP_STATUS" = "200" ]; then
    check_result 0 "Frontend is accessible (HTTP $HTTP_STATUS)"
else
    check_result 1 "Frontend returned HTTP $HTTP_STATUS"
fi

# 2. Check for JavaScript errors in HTML
echo -e "\n📋 Test 2: JavaScript Error Check"
HTML_CONTENT=$(curl -s http://localhost:3000)
if echo "$HTML_CONTENT" | grep -q "Cannot access.*before initialization"; then
    check_result 1 "JavaScript initialization error still present"
else
    check_result 0 "No JavaScript initialization errors in HTML"
fi

# 3. Check vendor file is properly served
echo -e "\n📋 Test 3: Vendor Bundle Check"
VENDOR_FILE=$(echo "$HTML_CONTENT" | grep -oE 'vendor-[a-zA-Z0-9]+\.js' | head -1)
if [ -n "$VENDOR_FILE" ]; then
    VENDOR_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:3000/assets/js/$VENDOR_FILE")
    if [ "$VENDOR_STATUS" = "200" ]; then
        check_result 0 "Vendor bundle served successfully"
    else
        check_result 1 "Vendor bundle not accessible (HTTP $VENDOR_STATUS)"
    fi
else
    check_result 1 "Vendor bundle not found in HTML"
fi

# 4. Check API health
echo -e "\n📋 Test 4: API Health Check"
API_HEALTH=$(curl -s http://localhost:5001/api/health)
API_STATUS=$(echo "$API_HEALTH" | jq -r '.status' 2>/dev/null || echo "error")
if [ "$API_STATUS" != "error" ]; then
    check_result 0 "API is responding (status: $API_STATUS)"
else
    check_result 1 "API health check failed"
fi

# 5. Check for console errors pattern
echo -e "\n📋 Test 5: Console Error Pattern Check"
# Check if vendor file contains problematic patterns
if [ -n "$VENDOR_FILE" ]; then
    VENDOR_CONTENT=$(curl -s "http://localhost:3000/assets/js/$VENDOR_FILE" | head -c 5000)
    # Look for temporal dead zone patterns
    if echo "$VENDOR_CONTENT" | grep -q 'hoist_vars.*true\|hoist_funs.*true'; then
        check_result 1 "Vendor file still contains problematic hoisting"
    else
        check_result 0 "Vendor file appears to be safely minified"
    fi
fi

# 6. Test login page functionality
echo -e "\n📋 Test 6: Login Page Test"
LOGIN_PAGE=$(curl -s http://localhost:3000/auth/signin)
if echo "$LOGIN_PAGE" | grep -q 'input.*name="email"\|data-testid="email-input"'; then
    check_result 0 "Login page renders correctly"
else
    check_result 1 "Login page not rendering properly"
fi

# 7. Check build configuration
echo -e "\n📋 Test 7: Build Configuration Check"
if [ -f "packages/frontend/vite.config.ts" ]; then
    if grep -q "hoist_vars: false" packages/frontend/vite.config.ts && \
       grep -q "hoist_funs: false" packages/frontend/vite.config.ts; then
        check_result 0 "Vite config has safe terser settings"
    else
        check_result 1 "Vite config missing safe terser settings"
    fi
else
    check_result 1 "Vite config not found"
fi

# 8. Check lazy loading error handling
echo -e "\n📋 Test 8: Lazy Loading Configuration"
if [ -f "packages/frontend/src/utils/lazyLoad.ts" ]; then
    if grep -q "catch.*Error.*console.error" packages/frontend/src/utils/lazyLoad.ts; then
        check_result 0 "Lazy loading has proper error handling"
    else
        check_result 1 "Lazy loading missing error handling"
    fi
else
    check_result 1 "Lazy loading utility not found"
fi

# 9. Test WebSocket connection
echo -e "\n📋 Test 9: WebSocket Connection Test"
# Simple check if socket.io is included
if echo "$HTML_CONTENT" | grep -q "socket.io\|socketio"; then
    check_result 0 "WebSocket client library included"
else
    check_result 1 "WebSocket client library not found"
fi

# 10. Final application load test
echo -e "\n📋 Test 10: Application Load Test"
# Check if React app mount point exists
if echo "$HTML_CONTENT" | grep -q 'id="root"'; then
    check_result 0 "React mount point exists"
else
    check_result 1 "React mount point missing"
fi

# Summary
echo -e "\n=================================================="
echo -e "📊 Test Summary:"
echo -e "   ${GREEN}Passed: $TESTS_PASSED${NC}"
echo -e "   ${RED}Failed: $TESTS_FAILED${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}✅ All tests passed! The production fix appears to be working.${NC}"
    echo -e "\n${YELLOW}⚠️  Note: Please test on actual production URL (spherosegapp.utia.cas.cz) to confirm.${NC}"
    exit 0
else
    echo -e "\n${RED}❌ Some tests failed. Please review the issues above.${NC}"
    exit 1
fi