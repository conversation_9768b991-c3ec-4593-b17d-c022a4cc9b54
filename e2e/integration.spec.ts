import { test, expect } from '@playwright/test';
import { test as baseTest } from './fixtures/base';

// Cross-Service Integration E2E Tests
test.describe('Cross-Service Integration', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/auth/signin');
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'testuser123');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
  });

  // Frontend-Backend Integration Tests
  test.describe('Frontend-Backend Integration', () => {
    test('should handle real-time updates via WebSocket', async ({ page, context }) => {
      // Open two browser tabs
      const page2 = await context.newPage();
      
      // Login in second tab
      await page2.goto('/auth/signin');
      await page2.fill('[name="email"]', '<EMAIL>');
      await page2.fill('[name="password"]', 'testuser123');
      await page2.click('button[type="submit"]');
      await page2.waitForURL('**/dashboard');
      
      // Create project in first tab
      await page.click('button:has-text("Create New Project")');
      await page.fill('[name="name"]', 'Real-time Test Project');
      await page.fill('[name="description"]', 'Testing WebSocket updates');
      await page.click('button:has-text("Create Project")');
      
      // Verify project appears in second tab without refresh
      await expect(page2.locator('text=Real-time Test Project')).toBeVisible({ timeout: 5000 });
      
      // Clean up
      await page2.close();
    });

    test('should sync data between multiple sessions', async ({ page, browser }) => {
      // Create new browser context (different session)
      const context2 = await browser.newContext();
      const page2 = await context2.newPage();
      
      // Login with same user
      await page2.goto('/auth/signin');
      await page2.fill('[name="email"]', '<EMAIL>');
      await page2.fill('[name="password"]', 'testuser123');
      await page2.click('button[type="submit"]');
      await page2.waitForURL('**/dashboard');
      
      // Get initial project count
      const projectCards = page.locator('.project-card');
      const initialCount = await projectCards.count();
      
      // Create project in second session
      await page2.click('button:has-text("Create New Project")');
      await page2.fill('[name="name"]', 'Cross-Session Project');
      await page2.click('button:has-text("Create Project")');
      
      // Verify appears in first session
      await page.reload();
      const newCount = await projectCards.count();
      expect(newCount).toBe(initialCount + 1);
      
      // Clean up
      await context2.close();
    });

    test('should maintain consistency across API calls', async ({ page }) => {
      // Create a project
      await page.click('button:has-text("Create New Project")');
      const projectName = `Consistency Test ${Date.now()}`;
      await page.fill('[name="name"]', projectName);
      await page.click('button:has-text("Create Project")');
      
      // Wait for creation
      await expect(page.locator(`text=${projectName}`)).toBeVisible();
      
      // Get project data via API
      const response = await page.request.get('/api/projects');
      const projects = await response.json();
      const project = projects.find(p => p.name === projectName);
      
      expect(project).toBeTruthy();
      expect(project.name).toBe(projectName);
      
      // Verify UI matches API data
      await page.click(`text=${projectName}`);
      await expect(page.locator('[data-testid="project-id"]')).toContainText(project.id.toString());
    });
  });

  // Backend-ML Service Integration Tests
  test.describe('Backend-ML Integration', () => {
    test('should process segmentation through complete pipeline', async ({ page }) => {
      // Navigate to a project
      await page.click('.project-card').first();
      
      // Upload an image
      const [fileChooser] = await Promise.all([
        page.waitForEvent('filechooser'),
        page.click('button:has-text("Upload Images")')
      ]);
      
      await fileChooser.setFiles({
        name: 'test-cell.jpg',
        mimeType: 'image/jpeg',
        buffer: Buffer.from('fake-image-data') // In real test, use actual image
      });
      
      // Wait for upload
      await expect(page.locator('text=Upload completed')).toBeVisible();
      
      // Start segmentation
      await page.click('[data-testid="image-card"]').first();
      await page.click('button:has-text("Start Segmentation")');
      
      // Monitor status changes
      await expect(page.locator('text=Queued')).toBeVisible();
      await expect(page.locator('text=Processing')).toBeVisible({ timeout: 30000 });
      
      // Should eventually complete (or fail if ML service not running)
      const result = await page.waitForSelector('text=/Completed|Failed/', { timeout: 60000 });
      const status = await result.textContent();
      
      // If ML service is running, should complete
      if (status === 'Completed') {
        await expect(page.locator('[data-testid="segmentation-result"]')).toBeVisible();
        await expect(page.locator('[data-testid="polygon-count"]')).toBeVisible();
      }
    });

    test('should handle ML service errors gracefully', async ({ page }) => {
      // Simulate ML service being down by making direct API call
      const response = await page.request.post('/api/segmentation/process', {
        data: {
          imageId: 999999, // Non-existent image
          projectId: 1
        }
      });
      
      // Should return error, not crash
      expect(response.status()).toBe(404);
      const error = await response.json();
      expect(error.error).toContain('not found');
    });

    test('should queue multiple segmentation tasks', async ({ page }) => {
      // Navigate to project
      await page.click('.project-card').first();
      
      // Select multiple images
      await page.click('[data-testid="select-all-images"]');
      
      // Start batch segmentation
      await page.click('button:has-text("Segment Selected")');
      await page.click('button:has-text("Confirm")');
      
      // Should show queue status
      await expect(page.locator('[data-testid="queue-status"]')).toBeVisible();
      await expect(page.locator('[data-testid="queue-count"]')).toBeVisible();
      
      // Verify items are queued
      const queueCount = await page.locator('[data-testid="queue-count"]').textContent();
      expect(parseInt(queueCount || '0')).toBeGreaterThan(0);
    });
  });

  // Backend-Database Integration Tests
  test.describe('Backend-Database Integration', () => {
    test('should handle database transactions correctly', async ({ page }) => {
      // Create project with images in single transaction
      await page.click('button:has-text("Create New Project")');
      await page.fill('[name="name"]', 'Transaction Test Project');
      await page.click('[data-testid="add-images-on-create"]');
      
      // Add multiple images
      const [fileChooser] = await Promise.all([
        page.waitForEvent('filechooser'),
        page.click('[data-testid="choose-images"]')
      ]);
      
      // Upload multiple files
      await fileChooser.setFiles([
        { name: 'image1.jpg', mimeType: 'image/jpeg', buffer: Buffer.from('data1') },
        { name: 'image2.jpg', mimeType: 'image/jpeg', buffer: Buffer.from('data2') },
        { name: 'image3.jpg', mimeType: 'image/jpeg', buffer: Buffer.from('data3') }
      ]);
      
      // Create project
      await page.click('button:has-text("Create Project with Images")');
      
      // Should either create all or none (transaction)
      const success = await page.locator('text=Project created successfully').isVisible({ timeout: 5000 }).catch(() => false);
      
      if (success) {
        // Verify all images were created
        await page.click('text=Transaction Test Project');
        await expect(page.locator('[data-testid="image-card"]')).toHaveCount(3);
      } else {
        // Verify project wasn't created
        await expect(page.locator('text=Transaction Test Project')).not.toBeVisible();
      }
    });

    test('should maintain referential integrity', async ({ page }) => {
      // Try to access deleted project's images via API
      const response = await page.request.get('/api/projects/999999/images');
      
      // Should return 404, not database error
      expect(response.status()).toBe(404);
      
      // Try to create image for non-existent project
      const createResponse = await page.request.post('/api/images', {
        data: {
          projectId: 999999,
          filename: 'test.jpg'
        }
      });
      
      expect(createResponse.status()).toBe(400);
      const error = await createResponse.json();
      expect(error.error).toContain('Project not found');
    });

    test('should handle concurrent database operations', async ({ page, context }) => {
      // Create multiple pages
      const pages = await Promise.all([
        context.newPage(),
        context.newPage(),
        context.newPage()
      ]);
      
      // Login on all pages
      for (const p of pages) {
        await p.goto('/auth/signin');
        await p.fill('[name="email"]', '<EMAIL>');
        await p.fill('[name="password"]', 'testuser123');
        await p.click('button[type="submit"]');
        await p.waitForURL('**/dashboard');
      }
      
      // Create projects concurrently
      const promises = pages.map(async (p, index) => {
        await p.click('button:has-text("Create New Project")');
        await p.fill('[name="name"]', `Concurrent Project ${index}`);
        await p.click('button:has-text("Create Project")');
        return p.waitForSelector('text=Project created successfully', { timeout: 10000 });
      });
      
      // All should succeed without conflicts
      const results = await Promise.allSettled(promises);
      const successes = results.filter(r => r.status === 'fulfilled').length;
      expect(successes).toBe(3);
      
      // Clean up
      for (const p of pages) {
        await p.close();
      }
    });
  });

  // Frontend-Backend-Database Integration Tests
  test.describe('Full Stack Integration', () => {
    test('should handle complete user workflow', async ({ page }) => {
      // Create project
      await page.click('button:has-text("Create New Project")');
      const projectName = `Full Stack Test ${Date.now()}`;
      await page.fill('[name="name"]', projectName);
      await page.fill('[name="description"]', 'Testing complete integration');
      await page.click('button:has-text("Create Project")');
      
      // Navigate to project
      await page.click(`text=${projectName}`);
      
      // Upload image
      const [fileChooser] = await Promise.all([
        page.waitForEvent('filechooser'),
        page.click('button:has-text("Upload Images")')
      ]);
      
      await fileChooser.setFiles({
        name: 'integration-test.jpg',
        mimeType: 'image/jpeg',
        buffer: Buffer.from('test-image-data')
      });
      
      // Wait for upload
      await expect(page.locator('text=Upload completed')).toBeVisible();
      
      // Edit project details
      await page.click('[data-testid="edit-project"]');
      await page.fill('[name="description"]', 'Updated description');
      await page.click('button:has-text("Save Changes")');
      
      // Verify persistence
      await page.reload();
      await expect(page.locator('text=Updated description')).toBeVisible();
      
      // Export data
      await page.click('button:has-text("Export")');
      await page.click('text=Export as JSON');
      
      // Wait for download
      const download = await page.waitForEvent('download');
      expect(download.suggestedFilename()).toContain('export');
      
      // Delete project
      await page.click('[data-testid="project-settings"]');
      await page.click('button:has-text("Delete Project")');
      await page.click('button:has-text("Confirm Delete")');
      
      // Verify deletion
      await expect(page).toHaveURL('**/dashboard');
      await expect(page.locator(`text=${projectName}`)).not.toBeVisible();
    });

    test('should maintain data consistency across services', async ({ page }) => {
      // Get initial statistics
      await page.goto('/profile');
      const initialProjects = await page.locator('[data-testid="total-projects"]').textContent();
      const initialImages = await page.locator('[data-testid="total-images"]').textContent();
      
      // Create project with images
      await page.goto('/dashboard');
      await page.click('button:has-text("Create New Project")');
      await page.fill('[name="name"]', 'Stats Test Project');
      await page.click('button:has-text("Create Project")');
      
      await page.click('text=Stats Test Project');
      
      // Upload multiple images
      const [fileChooser] = await Promise.all([
        page.waitForEvent('filechooser'),
        page.click('button:has-text("Upload Images")')
      ]);
      
      await fileChooser.setFiles([
        { name: 'img1.jpg', mimeType: 'image/jpeg', buffer: Buffer.from('data1') },
        { name: 'img2.jpg', mimeType: 'image/jpeg', buffer: Buffer.from('data2') }
      ]);
      
      await expect(page.locator('text=Upload completed')).toBeVisible();
      
      // Check updated statistics
      await page.goto('/profile');
      const newProjects = await page.locator('[data-testid="total-projects"]').textContent();
      const newImages = await page.locator('[data-testid="total-images"]').textContent();
      
      expect(parseInt(newProjects || '0')).toBe(parseInt(initialProjects || '0') + 1);
      expect(parseInt(newImages || '0')).toBe(parseInt(initialImages || '0') + 2);
      
      // Verify via API
      const response = await page.request.get('/api/users/stats');
      const stats = await response.json();
      
      expect(stats.totalProjects).toBe(parseInt(newProjects || '0'));
      expect(stats.totalImages).toBe(parseInt(newImages || '0'));
    });
  });

  // Service Health Integration Tests
  test.describe('Service Health Monitoring', () => {
    test('should report accurate health status for all services', async ({ page }) => {
      const response = await page.request.get('/api/health');
      const health = await response.json();
      
      // Check overall status
      expect(health.status).toMatch(/healthy|degraded/);
      
      // Check individual services
      expect(health.services).toHaveProperty('database');
      expect(health.services).toHaveProperty('redis');
      expect(health.services).toHaveProperty('rabbitmq');
      expect(health.services).toHaveProperty('ml');
      
      // Verify service details
      expect(health.services.database.status).toMatch(/up|down/);
      expect(health.services.redis.status).toMatch(/up|down/);
      
      // Check response times
      if (health.services.database.status === 'up') {
        expect(health.services.database.responseTime).toBeLessThan(1000);
      }
    });

    test('should handle service degradation gracefully', async ({ page }) => {
      // Even if some services are down, core functionality should work
      await page.goto('/dashboard');
      
      // Should be able to view projects
      await expect(page.locator('.project-card').first()).toBeVisible();
      
      // Should show warning if services are degraded
      const healthIndicator = page.locator('[data-testid="health-indicator"]');
      if (await healthIndicator.isVisible()) {
        const status = await healthIndicator.getAttribute('data-status');
        if (status === 'degraded') {
          await expect(page.locator('text=/ML service unavailable|Redis unavailable/')).toBeVisible();
        }
      }
    });
  });
});