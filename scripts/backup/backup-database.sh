#!/bin/bash
# Database Backup Script for SpherosegV4
# Performs automated PostgreSQL backups with encryption and rotation

set -euo pipefail

# Configuration
BACKUP_DIR="/home/<USER>/spheroseg/backups"
DB_CONTAINER="spheroseg-db"
DB_NAME="spheroseg"
DB_USER="spheroseg_prod"
RETENTION_DAYS=30
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILENAME="spheroseg_backup_${TIMESTAMP}.sql.gz.enc"

# Load environment variables
if [ -f /home/<USER>/spheroseg/.env.production.secure ]; then
    source /home/<USER>/spheroseg/.env.production.secure
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Log functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# Create backup directory if it doesn't exist
mkdir -p "${BACKUP_DIR}"

log_info "Starting database backup..."

# Check if database container is running
if ! docker ps | grep -q "${DB_CONTAINER}"; then
    log_error "Database container ${DB_CONTAINER} is not running!"
    exit 1
fi

# Perform the backup
log_info "Creating backup: ${BACKUP_FILENAME}"
docker exec "${DB_CONTAINER}" pg_dump -U "${DB_USER}" "${DB_NAME}" | \
    gzip | \
    openssl enc -aes-256-cbc -salt -k "${BACKUP_ENCRYPTION_KEY:-defaultkey}" -pbkdf2 > "${BACKUP_DIR}/${BACKUP_FILENAME}"

# Check if backup was successful
if [ $? -eq 0 ] && [ -f "${BACKUP_DIR}/${BACKUP_FILENAME}" ]; then
    BACKUP_SIZE=$(ls -lh "${BACKUP_DIR}/${BACKUP_FILENAME}" | awk '{print $5}')
    log_info "Backup completed successfully. Size: ${BACKUP_SIZE}"
    
    # Test the backup integrity
    if openssl enc -aes-256-cbc -d -salt -k "${BACKUP_ENCRYPTION_KEY:-defaultkey}" -pbkdf2 -in "${BACKUP_DIR}/${BACKUP_FILENAME}" | gzip -t 2>/dev/null; then
        log_info "Backup integrity verified"
    else
        log_error "Backup integrity check failed!"
        exit 1
    fi
else
    log_error "Backup failed!"
    exit 1
fi

# Rotate old backups
log_info "Rotating old backups (keeping last ${RETENTION_DAYS} days)..."
find "${BACKUP_DIR}" -name "spheroseg_backup_*.sql.gz.enc" -mtime +${RETENTION_DAYS} -type f -delete

# Count remaining backups
BACKUP_COUNT=$(find "${BACKUP_DIR}" -name "spheroseg_backup_*.sql.gz.enc" -type f | wc -l)
log_info "Total backups retained: ${BACKUP_COUNT}"

# Optional: Upload to S3 or remote storage
if [ -n "${BACKUP_S3_BUCKET:-}" ]; then
    log_info "Uploading backup to S3..."
    aws s3 cp "${BACKUP_DIR}/${BACKUP_FILENAME}" "s3://${BACKUP_S3_BUCKET}/database-backups/${BACKUP_FILENAME}" || log_warn "S3 upload failed"
fi

# Send notification (optional)
if [ -n "${SLACK_WEBHOOK_URL:-}" ]; then
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"Database backup completed successfully: ${BACKUP_FILENAME} (${BACKUP_SIZE})\"}" \
        "${SLACK_WEBHOOK_URL}" 2>/dev/null || log_warn "Slack notification failed"
fi

log_info "Backup process completed"

# Log backup history
echo "$(date '+%Y-%m-%d %H:%M:%S'),${BACKUP_FILENAME},${BACKUP_SIZE},success" >> "${BACKUP_DIR}/backup_history.csv"