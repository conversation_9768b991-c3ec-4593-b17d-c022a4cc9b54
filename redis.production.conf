# Redis Configuration for Production
# High-security configuration with password protection

# Network binding - only internal network
bind 127.0.0.1 ::1
protected-mode yes
port 6379

# Authentication - MUST CHANGE IN PRODUCTION
requirepass xB4fG7jL9mP2qR5tW8yC

# Persistence - both RDB and AOF for maximum durability
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# Append only file
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Security - disable dangerous commands
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONF<PERSON> ""
rename-command SHUTDOWN ""
rename-command DEBUG ""

# Performance
maxmemory 512mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Logging
loglevel notice
logfile /var/log/redis/redis-server.log
syslog-enabled yes
syslog-ident redis

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Latency monitoring
latency-monitor-threshold 100

# Advanced config
hz 10
tcp-backlog 511
timeout 300
tcp-keepalive 300

# Disable potentially dangerous features
enable-protected-configs yes
enable-debug-command no
enable-module-command no