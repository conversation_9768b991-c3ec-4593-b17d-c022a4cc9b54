version: '3.8'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: spheroseg-elasticsearch
    restart: always
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - cluster.name=spheroseg-cluster
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - spheroseg-network
    deploy:
      resources:
        limits:
          memory: 2048M
        reservations:
          memory: 1024M

  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: spheroseg-logstash
    restart: always
    environment:
      - "LS_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "5044:5044"
      - "9600:9600"
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline:ro
      - ./monitoring/logstash/config/logstash.yml:/usr/share/logstash/config/logstash.yml:ro
    depends_on:
      - elasticsearch
    networks:
      - spheroseg-network
    deploy:
      resources:
        limits:
          memory: 1024M
        reservations:
          memory: 512M

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: spheroseg-kibana
    restart: always
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - SERVER_NAME=spheroseg-kibana
      - SERVER_HOST=0.0.0.0
      - SERVER_BASEPATH=/kibana
      - SERVER_REWRITEBASEPATH=true
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - spheroseg-network
    deploy:
      resources:
        limits:
          memory: 1024M
        reservations:
          memory: 512M

  filebeat:
    image: docker.elastic.co/beats/filebeat:8.11.0
    container_name: spheroseg-filebeat
    restart: always
    user: root
    volumes:
      - ./monitoring/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - filebeat_data:/usr/share/filebeat/data
    depends_on:
      - elasticsearch
      - logstash
    networks:
      - spheroseg-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

volumes:
  elasticsearch_data:
  filebeat_data:

networks:
  spheroseg-network:
    external: true