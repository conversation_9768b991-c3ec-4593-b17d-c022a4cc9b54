/**
 * Shared image utilities
 */

export interface ImageBase {
  id: string;
  name: string;
  path: string;
}

export interface ImageDimensions {
  width: number;
  height: number;
}

export interface ImageLoadOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
}

export interface ImageData {
  id: string;
  project_id: string;
  user_id: string;
  name: string;
  storage_path: string;
  thumbnail_path?: string | null;
  file_size?: number;
  created_at?: Date | string;
  updated_at?: Date | string;
  width?: number;
  height?: number;
  segmentation_status?: string;
  [key: string]: unknown;
}

export const IMAGE_FORMATS = {
  PNG: 'png',
  JPEG: 'jpeg',
  JPG: 'jpg',
  TIFF: 'tiff',
  TIF: 'tif',
  BMP: 'bmp',
} as const;

export const SUPPORTED_IMAGE_EXTENSIONS = Object.values(IMAGE_FORMATS);

export const SUPPORTED_IMAGE_MIME_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/tiff',
  'image/bmp'
];

export function isImageFormatSupported(extension: string): boolean {
  return SUPPORTED_IMAGE_EXTENSIONS.includes(extension.toLowerCase() as typeof SUPPORTED_IMAGE_EXTENSIONS[number]);
}

export function getImageExtension(filename: string): string {
  const parts = filename.split('.');
  return parts[parts.length - 1]?.toLowerCase() || '';
}

export function isImage(filename: string): boolean {
  const ext = getImageExtension(filename);
  return isImageFormatSupported(ext);
}

export function validateImageFile(file: { type: string; size: number }): void {
  if (!file) {
    throw new Error('No file provided');
  }

  if (!isValidImageFormat(file.type)) {
    throw new Error(`Unsupported file type: ${file.type}`);
  }

  // Check file size (max 10MB)
  const maxSize = 10 * 1024 * 1024;
  if (file.size > maxSize) {
    throw new Error(`File too large: ${file.size} bytes. Maximum allowed: ${maxSize} bytes`);
  }

  // Check minimum file size (1KB)
  const minSize = 1024;
  if (file.size < minSize) {
    throw new Error(`File too small: ${file.size} bytes. Minimum required: ${minSize} bytes`);
  }
}

export function getImageDimensions(width: number, height: number): ImageDimensions {
  if (width < 0 || height < 0) {
    throw new Error('Image dimensions cannot be negative');
  }
  return { width, height };
}



export function getSupportedImageFormats(): string[] {
  return [...SUPPORTED_IMAGE_MIME_TYPES];
}

export function isValidImageFormat(mimeType: string): boolean {
  if (!mimeType || typeof mimeType !== 'string') {
    return false;
  }
  return SUPPORTED_IMAGE_MIME_TYPES.includes(mimeType.toLowerCase());
}

const imageUtils = {
  getImagePath: (imagePath: string): string => {
    return imagePath;
  },
  
  getImageDimensions: (width: number, height: number): ImageDimensions => {
    return { width, height };
  },
  
  isImageFormatSupported,
  getImageExtension,
  isImage,
};

export default imageUtils;