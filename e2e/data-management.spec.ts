import { test, expect } from '@playwright/test';
import { test as baseTest } from './fixtures/base';

// Data Management and GDPR Compliance E2E Tests
test.describe('Data Management and GDPR Compliance', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/auth/signin');
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'testuser123');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
  });

  // Data Export Tests (GDPR Article 20 - Right to Data Portability)
  test.describe('Data Export and Portability', () => {
    test('should allow full account data export', async ({ page }) => {
      await page.goto('/profile/settings');
      await page.click('text=Privacy');
      
      // Request data export
      await page.click('button:has-text("Export All My Data")');
      
      // Should show export options
      await expect(page.locator('text=Select data to export')).toBeVisible();
      
      // Select all data types
      await page.check('[name="exportProfile"]');
      await page.check('[name="exportProjects"]');
      await page.check('[name="exportImages"]');
      await page.check('[name="exportSegmentations"]');
      await page.check('[name="exportActivityLog"]');
      
      // Choose format
      await page.selectOption('[name="exportFormat"]', 'json');
      
      // Start export
      await page.click('button:has-text("Start Export")');
      
      // Should show progress
      await expect(page.locator('[data-testid="export-progress"]')).toBeVisible();
      await expect(page.locator('text=Preparing your data export')).toBeVisible();
      
      // Wait for completion (or email notification)
      const downloadPromise = page.waitForEvent('download', { timeout: 60000 });
      const download = await downloadPromise;
      
      // Verify download
      expect(download.suggestedFilename()).toMatch(/user-data-export.*\.json/);
      
      // Save and verify content structure
      const path = await download.path();
      expect(path).toBeTruthy();
    });

    test('should export data in machine-readable format', async ({ page }) => {
      await page.goto('/profile/settings');
      await page.click('text=Privacy');
      
      // Quick export
      await page.click('button:has-text("Quick Export")');
      
      // Download should start
      const download = await page.waitForEvent('download');
      const content = await download.path();
      
      // Verify JSON structure (would need to read file in real test)
      expect(download.suggestedFilename()).toContain('.json');
    });

    test('should allow selective data export', async ({ page }) => {
      // Navigate to specific project
      await page.click('.project-card').first();
      
      // Export single project
      await page.click('[data-testid="project-menu"]');
      await page.click('text=Export Project Data');
      
      // Choose what to include
      await expect(page.locator('text=Export Options')).toBeVisible();
      await page.check('[name="includeImages"]');
      await page.check('[name="includeSegmentations"]');
      await page.check('[name="includeMetadata"]');
      await page.uncheck('[name="includeRawData"]');
      
      // Export
      await page.click('button:has-text("Export Project")');
      
      const download = await page.waitForEvent('download');
      expect(download.suggestedFilename()).toContain('project-export');
    });
  });

  // Data Deletion Tests (GDPR Article 17 - Right to Erasure)
  test.describe('Data Deletion and Right to be Forgotten', () => {
    test('should allow deletion of specific data', async ({ page }) => {
      // Navigate to project
      await page.click('.project-card').first();
      
      // Delete specific image
      await page.click('[data-testid="image-card"]').first();
      await page.click('button:has-text("Delete Image")');
      
      // Confirm deletion
      await expect(page.locator('text=This will permanently delete')).toBeVisible();
      await page.click('button:has-text("Confirm Delete")');
      
      // Verify deletion
      await expect(page.locator('text=Image deleted successfully')).toBeVisible();
      
      // Image should be gone
      await page.reload();
      await expect(page.locator('[data-testid="deleted-image-id"]')).not.toBeVisible();
    });

    test('should cascade delete related data', async ({ page }) => {
      // Create project with related data
      await page.click('button:has-text("Create New Project")');
      const projectName = `GDPR Test ${Date.now()}`;
      await page.fill('[name="name"]', projectName);
      await page.click('button:has-text("Create Project")');
      
      // Add some data
      await page.click(`text=${projectName}`);
      
      // Delete project
      await page.click('[data-testid="project-settings"]');
      await page.click('button:has-text("Delete Project")');
      
      // Should warn about cascade deletion
      await expect(page.locator('text=This will also delete:')).toBeVisible();
      await expect(page.locator('text=All images in this project')).toBeVisible();
      await expect(page.locator('text=All segmentation results')).toBeVisible();
      await expect(page.locator('text=All associated metadata')).toBeVisible();
      
      // Confirm
      await page.fill('[name="confirmProjectName"]', projectName);
      await page.click('button:has-text("Delete Everything")');
      
      // Verify complete deletion
      await expect(page).toHaveURL('**/dashboard');
      await expect(page.locator(`text=${projectName}`)).not.toBeVisible();
    });

    test('should provide account deletion with grace period', async ({ page }) => {
      await page.goto('/profile/settings');
      await page.click('text=Privacy');
      
      // Request account deletion
      await page.locator('[data-testid="danger-zone"]').scrollIntoViewIfNeeded();
      await page.click('button:has-text("Delete My Account")');
      
      // Should show comprehensive warning
      await expect(page.locator('text=Account Deletion Request')).toBeVisible();
      await expect(page.locator('text=This action will:')).toBeVisible();
      await expect(page.locator('text=Delete all your projects')).toBeVisible();
      await expect(page.locator('text=Remove all your personal data')).toBeVisible();
      await expect(page.locator('text=Cancel your subscription')).toBeVisible();
      
      // Should mention grace period
      await expect(page.locator('text=30-day grace period')).toBeVisible();
      await expect(page.locator('text=You can cancel this request')).toBeVisible();
      
      // Enter confirmation
      await page.fill('[name="confirmDelete"]', 'DELETE MY ACCOUNT');
      await page.fill('[name="password"]', 'testuser123');
      await page.fill('[name="reason"]', 'Testing GDPR compliance');
      
      // Cancel (don't actually delete)
      await page.click('button:has-text("Cancel")');
    });
  });

  // Data Access Tests (GDPR Article 15 - Right of Access)
  test.describe('Data Access and Transparency', () => {
    test('should provide data access dashboard', async ({ page }) => {
      await page.goto('/profile/data-privacy');
      
      // Should show data categories
      await expect(page.locator('text=Your Data Overview')).toBeVisible();
      await expect(page.locator('[data-testid="data-category-personal"]')).toBeVisible();
      await expect(page.locator('[data-testid="data-category-projects"]')).toBeVisible();
      await expect(page.locator('[data-testid="data-category-usage"]')).toBeVisible();
      await expect(page.locator('[data-testid="data-category-security"]')).toBeVisible();
    });

    test('should show what data is collected', async ({ page }) => {
      await page.goto('/profile/data-privacy');
      await page.click('text=Personal Information');
      
      // Should list all collected data
      await expect(page.locator('text=Email Address')).toBeVisible();
      await expect(page.locator('text=Name')).toBeVisible();
      await expect(page.locator('text=Organization')).toBeVisible();
      await expect(page.locator('text=Profile Picture')).toBeVisible();
      
      // Should show purpose
      await expect(page.locator('text=Why we collect this')).toBeVisible();
    });

    test('should provide activity log access', async ({ page }) => {
      await page.goto('/profile/activity-log');
      
      // Should show recent activities
      await expect(page.locator('[data-testid="activity-list"]')).toBeVisible();
      await expect(page.locator('[data-testid="activity-item"]').first()).toBeVisible();
      
      // Should be filterable
      await page.selectOption('[name="activityType"]', 'login');
      await expect(page.locator('text=Logged in')).toBeVisible();
      
      // Should be exportable
      await page.click('button:has-text("Export Activity Log")');
      const download = await page.waitForEvent('download');
      expect(download.suggestedFilename()).toContain('activity-log');
    });
  });

  // Data Rectification Tests (GDPR Article 16 - Right to Rectification)
  test.describe('Data Rectification', () => {
    test('should allow data correction', async ({ page }) => {
      await page.goto('/profile');
      await page.click('button:has-text("Edit Profile")');
      
      // Correct data
      const newEmail = '<EMAIL>';
      await page.fill('[name="email"]', newEmail);
      await page.fill('[name="firstName"]', 'Corrected');
      await page.fill('[name="lastName"]', 'Name');
      
      await page.click('button:has-text("Save Changes")');
      
      // Should update immediately
      await expect(page.locator('text=Profile updated')).toBeVisible();
      
      // Verify changes
      await page.reload();
      await expect(page.locator('[data-testid="profile-email"]')).toContainText(newEmail);
    });

    test('should maintain audit trail of changes', async ({ page }) => {
      await page.goto('/profile/activity-log');
      
      // Filter for profile changes
      await page.selectOption('[name="activityType"]', 'profile_update');
      
      // Should show change history
      await expect(page.locator('text=Profile updated')).toBeVisible();
      await expect(page.locator('[data-testid="change-details"]').first()).toBeVisible();
      
      // Click to see details
      await page.click('[data-testid="activity-item"]').first();
      
      // Should show what changed
      await expect(page.locator('text=Changed fields:')).toBeVisible();
      await expect(page.locator('text=Previous value:')).toBeVisible();
      await expect(page.locator('text=New value:')).toBeVisible();
    });
  });

  // Consent Management Tests (GDPR Article 7 - Consent)
  test.describe('Consent Management', () => {
    test('should provide granular consent controls', async ({ page }) => {
      await page.goto('/profile/privacy-settings');
      
      // Should show consent options
      await expect(page.locator('text=Privacy Preferences')).toBeVisible();
      
      // Marketing consent
      const marketingToggle = page.locator('[data-testid="consent-marketing"]');
      await expect(marketingToggle).toBeVisible();
      await expect(page.locator('text=Marketing communications')).toBeVisible();
      
      // Analytics consent
      const analyticsToggle = page.locator('[data-testid="consent-analytics"]');
      await expect(analyticsToggle).toBeVisible();
      await expect(page.locator('text=Usage analytics')).toBeVisible();
      
      // Third-party sharing
      const sharingToggle = page.locator('[data-testid="consent-sharing"]');
      await expect(sharingToggle).toBeVisible();
      await expect(page.locator('text=Third-party integrations')).toBeVisible();
      
      // Toggle consent
      await marketingToggle.click();
      await page.click('button:has-text("Save Preferences")');
      
      // Should be saved
      await expect(page.locator('text=Preferences updated')).toBeVisible();
    });

    test('should track consent history', async ({ page }) => {
      await page.goto('/profile/privacy-settings');
      await page.click('text=Consent History');
      
      // Should show consent timeline
      await expect(page.locator('[data-testid="consent-timeline"]')).toBeVisible();
      await expect(page.locator('[data-testid="consent-event"]').first()).toBeVisible();
      
      // Should show what was consented to
      await expect(page.locator('text=Consent given for:')).toBeVisible();
      await expect(page.locator('text=Date:')).toBeVisible();
      await expect(page.locator('text=IP Address:')).toBeVisible();
    });

    test('should allow consent withdrawal', async ({ page }) => {
      await page.goto('/profile/privacy-settings');
      
      // Withdraw all consent
      await page.click('button:has-text("Withdraw All Consent")');
      
      // Should show warning
      await expect(page.locator('text=This will limit functionality')).toBeVisible();
      await expect(page.locator('text=You can still use core features')).toBeVisible();
      
      // Confirm
      await page.click('button:has-text("Confirm Withdrawal")');
      
      // All toggles should be off
      await expect(page.locator('[data-testid="consent-marketing"]')).not.toBeChecked();
      await expect(page.locator('[data-testid="consent-analytics"]')).not.toBeChecked();
      await expect(page.locator('[data-testid="consent-sharing"]')).not.toBeChecked();
    });
  });

  // Data Minimization Tests
  test.describe('Data Minimization', () => {
    test('should only collect necessary data', async ({ page }) => {
      // Logout first
      await page.click('[data-testid="user-menu"]');
      await page.click('text=Sign Out');
      
      // Try to sign up
      await page.goto('/auth/signup');
      
      // Should only require essential fields
      const requiredFields = await page.locator('[required]').all();
      const fieldNames = await Promise.all(
        requiredFields.map(field => field.getAttribute('name'))
      );
      
      // Should only require email and password
      expect(fieldNames).toContain('email');
      expect(fieldNames).toContain('password');
      expect(fieldNames.length).toBeLessThanOrEqual(3); // email, password, confirm password
      
      // Optional fields should be clearly marked
      await expect(page.locator('text=Optional')).toBeVisible();
    });

    test('should auto-delete old data', async ({ page }) => {
      await page.goto('/profile/data-retention');
      
      // Should show retention policies
      await expect(page.locator('text=Data Retention Settings')).toBeVisible();
      await expect(page.locator('text=Activity logs: 90 days')).toBeVisible();
      await expect(page.locator('text=Deleted projects: 30 days')).toBeVisible();
      await expect(page.locator('text=Export files: 7 days')).toBeVisible();
      
      // Should allow configuration
      await page.selectOption('[name="activityLogRetention"]', '30');
      await page.click('button:has-text("Update Retention")');
      
      await expect(page.locator('text=Retention settings updated')).toBeVisible();
    });
  });

  // Third-party Data Sharing Tests
  test.describe('Third-party Data Controls', () => {
    test('should list all third-party integrations', async ({ page }) => {
      await page.goto('/profile/integrations');
      
      // Should show connected services
      await expect(page.locator('text=Connected Services')).toBeVisible();
      
      // Should show what data is shared
      const integrations = page.locator('[data-testid="integration-item"]');
      const count = await integrations.count();
      
      if (count > 0) {
        await integrations.first().click();
        await expect(page.locator('text=Data shared:')).toBeVisible();
        await expect(page.locator('text=Last synced:')).toBeVisible();
        await expect(page.locator('text=Permissions:')).toBeVisible();
      }
    });

    test('should allow revoking third-party access', async ({ page }) => {
      await page.goto('/profile/integrations');
      
      const integrations = page.locator('[data-testid="integration-item"]');
      const count = await integrations.count();
      
      if (count > 0) {
        await page.click('[data-testid="revoke-access"]').first();
        
        // Should show confirmation
        await expect(page.locator('text=Revoke Access?')).toBeVisible();
        await expect(page.locator('text=This service will no longer')).toBeVisible();
        
        await page.click('button:has-text("Revoke")');
        
        await expect(page.locator('text=Access revoked')).toBeVisible();
      }
    });
  });

  // Data Breach Notification Tests
  test.describe('Data Breach Notifications', () => {
    test('should display security notifications prominently', async ({ page }) => {
      // Check for any security banners
      await page.goto('/dashboard');
      
      // If there's a security notice, it should be prominent
      const securityBanner = page.locator('[data-testid="security-banner"]');
      if (await securityBanner.isVisible()) {
        await expect(securityBanner).toHaveCSS('background-color', /red|orange/);
        await expect(page.locator('text=/Security Notice|Important/')).toBeVisible();
      }
    });

    test('should provide detailed breach information', async ({ page }) => {
      await page.goto('/profile/security-notices');
      
      // Should show security history
      await expect(page.locator('text=Security Notices')).toBeVisible();
      
      const notices = page.locator('[data-testid="security-notice"]');
      const count = await notices.count();
      
      if (count > 0) {
        await notices.first().click();
        
        // Should show required GDPR information
        await expect(page.locator('text=Date discovered:')).toBeVisible();
        await expect(page.locator('text=Data affected:')).toBeVisible();
        await expect(page.locator('text=Actions taken:')).toBeVisible();
        await expect(page.locator('text=Recommendations:')).toBeVisible();
      }
    });
  });
});