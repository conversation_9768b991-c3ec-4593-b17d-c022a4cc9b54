<!DOCTYPE html>
<html>
<head>
    <title>Production Test</title>
</head>
<body>
    <h1>Testing Production Build</h1>
    <p>Check browser console for errors</p>
    
    <iframe id="app-frame" src="http://localhost:80/" width="100%" height="600"></iframe>
    
    <script>
        // Monitor for errors in the iframe
        let errorCount = 0;
        const originalError = console.error;
        console.error = function(...args) {
            errorCount++;
            console.log('[ERROR DETECTED]:', ...args);
            originalError.apply(console, args);
        };
        
        setTimeout(() => {
            console.log(`Total errors detected: ${errorCount}`);
            if (errorCount === 0) {
                console.log('✅ No JavaScript errors detected!');
            } else {
                console.log('❌ JavaScript errors were detected');
            }
        }, 5000);
    </script>
</body>
</html>