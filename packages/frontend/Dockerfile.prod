# Multi-stage Dockerfile for production build
# Stage 1: Build the application in monorepo context
FROM node:18-alpine AS builder

WORKDIR /app

# Install build dependencies
RUN apk add --no-cache python3 make g++ git

# Clear npm cache and temp files
RUN npm cache clean --force && \
    rm -rf /root/.npm /tmp/* /var/cache/apk/*

# Copy package files for monorepo structure
COPY package*.json ./
COPY turbo.json ./
COPY packages/frontend/package*.json ./packages/frontend/
COPY packages/shared/package*.json ./packages/shared/
COPY packages/types/package*.json ./packages/types/

# Install dependencies for all packages
RUN npm ci --legacy-peer-deps

# Copy all source code
COPY packages/frontend ./packages/frontend
COPY packages/shared ./packages/shared
COPY packages/types ./packages/types
COPY tsconfig.json ./
COPY tsconfig.base.json ./

# Skip building shared and types packages - they should be built on host

# Clean any existing build artifacts
RUN rm -rf packages/frontend/dist && \
    rm -rf packages/frontend/node_modules/.vite && \
    rm -rf .turbo && \
    rm -rf node_modules/.cache

# Build the application for production
ENV NODE_ENV=production
ENV VITE_API_URL=/api
ENV VITE_API_BASE_URL=/api
ENV VITE_API_AUTH_PREFIX=/auth
ENV VITE_API_USERS_PREFIX=/users
ENV VITE_ASSETS_URL=/assets

RUN npm run build --workspace=@spheroseg/frontend

# Stage 2: Serve with nginx
FROM nginx:alpine

# Install curl for health checks
RUN apk add --no-cache curl

# Remove default nginx content to ensure clean deployment
RUN rm -rf /usr/share/nginx/html/*

# Copy nginx configuration
COPY packages/frontend/nginx.conf /etc/nginx/nginx.conf

# Copy built assets from builder stage
COPY --from=builder /app/packages/frontend/dist /usr/share/nginx/html

# Ensure no cache files are present
RUN find /usr/share/nginx/html -name "*.cache" -type f -delete || true

# Expose port 80
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]