import type { Plugin } from 'vite';

/**
 * Vite plugin to fix Temporal Dead Zone (TDZ) issues and React initialization
 * Ensures React is available before any code tries to use it
 */
export default function fixTDZPlugin(): Plugin {
  return {
    name: 'fix-tdz',
    enforce: 'post',
    generateBundle(_options, bundle) {
      // Process all JavaScript chunks
      for (const [fileName, chunk] of Object.entries(bundle)) {
        if (chunk.type === 'chunk' && fileName.endsWith('.js')) {
          let code = chunk.code;
          
          // Skip if it's not a vendor chunk
          if (!fileName.includes('vendor') && !fileName.includes('react')) {
            continue;
          }

          // Replace all const with var to avoid TDZ
          code = code.replace(/\bconst\s+/g, 'var ');
          code = code.replace(/\blet\s+/g, 'var ');

          // Fix React references
          // Pattern 1: Fix minified React references like u.createElement
          code = code.replace(
            /\b([a-zA-Z_$][\w$]*)\.(createElement|Component|PureComponent|Fragment|createContext|forwardRef|memo|lazy|Suspense|useEffect|useState|useContext|useRef|useMemo|useCallback|useReducer|useLayoutEffect)\b/g,
            (match, varName, method) => {
              // If it's a short variable name (likely minified), add safety check
              if (varName.length <= 3 && varName !== 'React') {
                return `(typeof ${varName} !== 'undefined' && ${varName} || (typeof React !== 'undefined' && React)).${method}`;
              }
              return match;
            }
          );

          // Pattern 2: Add React initialization at the top of vendor chunks
          if (fileName.includes('vendor')) {
            code = `
// Ensure React is available globally
if (typeof window !== 'undefined' && !window.React) {
  try {
    // Try to find React in various places
    if (typeof React !== 'undefined') {
      window.React = React;
    } else {
      // Search for React in loaded modules
      var modules = (typeof __webpack_require__ !== 'undefined' && __webpack_require__.m) || {};
      for (var key in modules) {
        var mod = modules[key];
        if (mod && mod.exports && mod.exports.createElement && mod.exports.Component) {
          window.React = mod.exports;
          break;
        }
      }
    }
  } catch (e) {
    console.warn('Failed to ensure React availability:', e);
  }
}
${code}`;
          }

          // Update the chunk code
          chunk.code = code;
        }
      }
    }
  };
}