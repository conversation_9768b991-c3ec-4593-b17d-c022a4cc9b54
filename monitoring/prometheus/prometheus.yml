global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'spheroseg-monitor'

scrape_configs:
  # Node Exporter - System metrics
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
        labels:
          instance: 'spheroseg-host'

  # cAdvisor - Container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
        labels:
          instance: 'spheroseg-containers'

  # Backend API metrics
  - job_name: 'backend'
    static_configs:
      - targets: ['backend:5001']
        labels:
          service: 'spheroseg-backend'
    metrics_path: '/metrics'

  # PostgreSQL metrics (if postgres_exporter is added)
  - job_name: 'postgresql'
    static_configs:
      - targets: ['postgres-exporter:9187']
        labels:
          service: 'spheroseg-db'

  # Redis metrics (if redis_exporter is added)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
        labels:
          service: 'spheroseg-redis'

  # RabbitMQ metrics
  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq:15672']
        labels:
          service: 'spheroseg-rabbitmq'
    metrics_path: '/metrics'

  # ML Service metrics
  - job_name: 'ml-service'
    static_configs:
      - targets: ['ml:5002']
        labels:
          service: 'spheroseg-ml'
    metrics_path: '/metrics'