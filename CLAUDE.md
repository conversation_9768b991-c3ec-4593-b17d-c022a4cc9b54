# CLAUDE.md - SpherosegV4 Production Guide

## Quick Start

SpherosegV4 je aplikace pro segmentaci buněk využ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (ResUNet). Monorepo s mik<PERSON><PERSON><PERSON><PERSON><PERSON>.

**Stack**: React 18 + TypeScript + Vite | Node.js + Express + PostgreSQL | Python + PyTorch + Flask

## 🚀 Production Commands

```bash
# Clean build & deploy (RECOMMENDED)
./scripts/clean-deploy.sh

# Docker cleanup before build
./scripts/docker-cleanup.sh

# Start production
docker-compose --env-file .env.production --profile prod up -d

# View logs
docker-compose --profile prod logs -f [service-name]

# Quick status check
docker-compose --profile prod ps
```

## 🛠️ Development

```bash
# Start dev environment
docker-compose --profile dev up -d

# Code quality (BEFORE COMMIT)
npm run code:check
npm run code:fix

# Testing
npm run test
npm run e2e
```

## 📁 Key Directories

- `packages/frontend/` - React UI (port 3000 dev, 80 prod)
- `packages/backend/` - API server (port 5001)
- `packages/ml/` - ML service (port 5002)
- `.env.production` - Production config
- `scripts/` - Deployment & maintenance scripts

## 🔧 Common Tasks

### Fix JavaScript Errors
1. Check `packages/frontend/vite.config.ts` - minification settings
2. Clear caches: `./scripts/docker-cleanup.sh`
3. Rebuild: `docker-compose --profile prod build --no-cache`

### Database Access
```bash
docker-compose exec db psql -U postgres -d spheroseg
```

### Test User
- Email: <EMAIL>
- Password: testuser123

## 🚨 Production Issues

### White Screen / JS Errors
- TDZ errors: Fixed via custom Vite plugin
- Clear all caches before rebuild
- Check nginx config for asset serving

### Build Failures
- TypeScript errors in shared/types: Use simplified Dockerfile approach
- Missing dependencies: Check package.json in each package
- Docker space: Run cleanup script before build

### Service URLs
- Production: https://spherosegapp.utia.cas.cz
- API: https://spherosegapp.utia.cas.cz/api
- Health: https://spherosegapp.utia.cas.cz/api/health

## 📊 Monitoring

```bash
# Check all services
docker-compose --profile prod ps

# API health
curl https://spherosegapp.utia.cas.cz/api/health

# View logs
docker-compose --profile prod logs -f backend
```

## 🔐 Security

- JWT with RS256 algorithm
- CSRF protection enabled
- Rate limiting configured
- CORS restricted to production domain

## 📝 Notes

- **ALWAYS** run Docker cleanup before builds to save disk space
- Production uses nginx.prod.nossl.conf for local testing
- ML model file may be missing in dev (expected)
- Pre-commit hooks enforce code quality

## System Info
- Sudo password: Cinoykty
- Environment files: `.env.production`, `.env`
- Critical configs: `docker-compose.yml`, `nginx/*.conf`