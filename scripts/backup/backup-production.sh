#!/bin/bash
# Production Database Backup Script

set -e

# Configuration
BACKUP_DIR="/var/backups/spheroseg"
RETENTION_DAYS=7
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
DB_CONTAINER="spheroseg-db-1"

# Ensure backup directory exists
mkdir -p $BACKUP_DIR

echo "Starting database backup at $(date)"

# Perform backup
docker exec -t $DB_CONTAINER pg_dump -U postgres spheroseg | gzip > "$BACKUP_DIR/spheroseg_$TIMESTAMP.sql.gz"

# Verify backup
if [ -f "$BACKUP_DIR/spheroseg_$TIMESTAMP.sql.gz" ]; then
    SIZE=$(ls -lh "$BACKUP_DIR/spheroseg_$TIMESTAMP.sql.gz" | awk '{print $5}')
    echo "Backup completed successfully. Size: $SIZE"
else
    echo "ERROR: Backup failed!"
    exit 1
fi

# Clean old backups
echo "Cleaning backups older than $RETENTION_DAYS days"
find $BACKUP_DIR -name "spheroseg_*.sql.gz" -mtime +$RETENTION_DAYS -delete

# List remaining backups
echo "Current backups:"
ls -lh $BACKUP_DIR/spheroseg_*.sql.gz | tail -10

echo "Backup process completed at $(date)"