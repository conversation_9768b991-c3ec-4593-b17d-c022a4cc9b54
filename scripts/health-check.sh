#!/bin/bash
# Comprehensive Health Check Script for SpherosegV4
# Checks health of all services and reports status

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
BACKEND_URL="http://localhost:5001"
ML_URL="http://localhost:5002"
FRONTEND_URL="http://localhost"
DB_HOST="localhost"
DB_PORT="5432"
REDIS_HOST="localhost"
REDIS_PORT="6379"
RABBITMQ_HOST="localhost"
RABBITMQ_PORT="5672"

# Counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNINGS=0

# Results array
declare -a RESULTS

# Log functions
log_check() {
    echo -e "${BLUE}[CHECK]${NC} $1"
    ((TOTAL_CHECKS++))
}

log_pass() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((PASSED_CHECKS++))
    RESULTS+=("✓ $1")
}

log_fail() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((FAILED_CHECKS++))
    RESULTS+=("✗ $1")
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
    ((WARNINGS++))
    RESULTS+=("⚠ $1")
}

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

# Header
echo "================================================"
echo "SpherosegV4 Health Check - $(date '+%Y-%m-%d %H:%M:%S')"
echo "================================================"
echo ""

# 1. Check Docker Services
log_check "Checking Docker services..."
DOCKER_SERVICES=("spheroseg-db" "spheroseg-redis" "spheroseg-rabbitmq" "spheroseg-backend" "spheroseg-ml" "spheroseg-frontend" "spheroseg-nginx-prod")

for service in "${DOCKER_SERVICES[@]}"; do
    if docker ps --format "table {{.Names}}" | grep -q "^$service"; then
        log_pass "Docker service $service is running"
    else
        log_fail "Docker service $service is not running"
    fi
done

# 2. Check Database
log_check "Checking PostgreSQL database..."
if nc -z "$DB_HOST" "$DB_PORT" 2>/dev/null; then
    log_pass "PostgreSQL is listening on port $DB_PORT"
    
    # Try to connect to database
    if docker exec spheroseg-db pg_isready -U spheroseg_prod -d spheroseg >/dev/null 2>&1; then
        log_pass "PostgreSQL connection successful"
    else
        log_fail "PostgreSQL connection failed"
    fi
else
    log_fail "PostgreSQL is not listening on port $DB_PORT"
fi

# 3. Check Redis
log_check "Checking Redis..."
if nc -z "$REDIS_HOST" "$REDIS_PORT" 2>/dev/null; then
    log_pass "Redis is listening on port $REDIS_PORT"
    
    # Try to ping Redis
    if docker exec spheroseg-redis redis-cli ping | grep -q "PONG" 2>/dev/null; then
        log_pass "Redis connection successful"
    else
        log_fail "Redis connection failed"
    fi
else
    log_fail "Redis is not listening on port $REDIS_PORT"
fi

# 4. Check RabbitMQ
log_check "Checking RabbitMQ..."
if nc -z "$RABBITMQ_HOST" "$RABBITMQ_PORT" 2>/dev/null; then
    log_pass "RabbitMQ is listening on port $RABBITMQ_PORT"
else
    log_fail "RabbitMQ is not listening on port $RABBITMQ_PORT"
fi

# 5. Check Backend API
log_check "Checking Backend API..."
if curl -s -f "$BACKEND_URL/health" >/dev/null 2>&1; then
    log_pass "Backend API health endpoint responding"
    
    # Check API version
    VERSION=$(curl -s "$BACKEND_URL/v1/version" 2>/dev/null | grep -o '"version":"[^"]*"' | cut -d'"' -f4 || echo "unknown")
    log_info "Backend API version: $VERSION"
else
    log_fail "Backend API health endpoint not responding"
fi

# 6. Check ML Service
log_check "Checking ML Service..."
if curl -s -f "$ML_URL/health" >/dev/null 2>&1; then
    log_pass "ML Service health endpoint responding"
    
    # Check if model is loaded
    MODEL_STATUS=$(curl -s "$ML_URL/model/status" 2>/dev/null | grep -o '"loaded":[^,}]*' | cut -d':' -f2 || echo "false")
    if [ "$MODEL_STATUS" = "true" ]; then
        log_pass "ML model is loaded"
    else
        log_warn "ML model is not loaded"
    fi
else
    log_fail "ML Service health endpoint not responding"
fi

# 7. Check Frontend
log_check "Checking Frontend..."
if curl -s -f "$FRONTEND_URL" -o /dev/null 2>&1; then
    log_pass "Frontend is accessible"
    
    # Check if static assets are served
    if curl -s -f "$FRONTEND_URL/index.html" -o /dev/null 2>&1; then
        log_pass "Frontend static assets are served correctly"
    else
        log_warn "Frontend static assets may have issues"
    fi
else
    log_fail "Frontend is not accessible"
fi

# 8. Check SSL/HTTPS (if configured)
log_check "Checking SSL/HTTPS..."
if [ -f "/home/<USER>/spheroseg/ssl/fullchain.pem" ] && [ -f "/home/<USER>/spheroseg/ssl/privkey.pem" ]; then
    log_pass "SSL certificates found"
    
    # Check certificate expiry
    CERT_EXPIRY=$(openssl x509 -enddate -noout -in /home/<USER>/spheroseg/ssl/fullchain.pem 2>/dev/null | cut -d= -f2)
    if [ -n "$CERT_EXPIRY" ]; then
        log_info "SSL certificate expires: $CERT_EXPIRY"
        
        # Check if certificate expires within 30 days
        EXPIRY_EPOCH=$(date -d "$CERT_EXPIRY" +%s 2>/dev/null || echo 0)
        NOW_EPOCH=$(date +%s)
        DAYS_UNTIL_EXPIRY=$(( (EXPIRY_EPOCH - NOW_EPOCH) / 86400 ))
        
        if [ $DAYS_UNTIL_EXPIRY -lt 30 ] && [ $DAYS_UNTIL_EXPIRY -gt 0 ]; then
            log_warn "SSL certificate expires in $DAYS_UNTIL_EXPIRY days"
        elif [ $DAYS_UNTIL_EXPIRY -le 0 ]; then
            log_fail "SSL certificate has expired"
        else
            log_pass "SSL certificate is valid for $DAYS_UNTIL_EXPIRY more days"
        fi
    fi
else
    log_warn "SSL certificates not found"
fi

# 9. Check Disk Space
log_check "Checking disk space..."
DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -lt 80 ]; then
    log_pass "Disk usage is ${DISK_USAGE}%"
elif [ "$DISK_USAGE" -lt 90 ]; then
    log_warn "Disk usage is ${DISK_USAGE}% (getting high)"
else
    log_fail "Disk usage is ${DISK_USAGE}% (critical)"
fi

# 10. Check Memory Usage
log_check "Checking memory usage..."
MEM_USAGE=$(free | grep Mem | awk '{print int($3/$2 * 100)}')
if [ "$MEM_USAGE" -lt 80 ]; then
    log_pass "Memory usage is ${MEM_USAGE}%"
elif [ "$MEM_USAGE" -lt 90 ]; then
    log_warn "Memory usage is ${MEM_USAGE}% (getting high)"
else
    log_fail "Memory usage is ${MEM_USAGE}% (critical)"
fi

# 11. Check Monitoring Stack (if deployed)
log_check "Checking monitoring stack..."
MONITORING_SERVICES=("spheroseg-prometheus" "spheroseg-grafana" "spheroseg-alertmanager")

for service in "${MONITORING_SERVICES[@]}"; do
    if docker ps --format "table {{.Names}}" | grep -q "^$service"; then
        log_pass "Monitoring service $service is running"
    else
        log_warn "Monitoring service $service is not running"
    fi
done

# 12. Check Backup Status
log_check "Checking backup status..."
BACKUP_DIR="/home/<USER>/spheroseg/backups"
if [ -d "$BACKUP_DIR" ]; then
    LATEST_BACKUP=$(ls -t "$BACKUP_DIR"/spheroseg_backup_*.sql.gz.enc 2>/dev/null | head -1)
    if [ -n "$LATEST_BACKUP" ]; then
        BACKUP_AGE=$(( ($(date +%s) - $(stat -c %Y "$LATEST_BACKUP")) / 3600 ))
        if [ "$BACKUP_AGE" -lt 24 ]; then
            log_pass "Latest backup is $BACKUP_AGE hours old"
        elif [ "$BACKUP_AGE" -lt 48 ]; then
            log_warn "Latest backup is $BACKUP_AGE hours old"
        else
            log_fail "Latest backup is $BACKUP_AGE hours old (too old)"
        fi
    else
        log_warn "No backups found"
    fi
else
    log_warn "Backup directory not found"
fi

# Summary
echo ""
echo "================================================"
echo "Health Check Summary"
echo "================================================"
echo "Total Checks: $TOTAL_CHECKS"
echo "Passed: $PASSED_CHECKS"
echo "Failed: $FAILED_CHECKS"
echo "Warnings: $WARNINGS"
echo ""

# Calculate health score
HEALTH_SCORE=$(( (PASSED_CHECKS * 100) / TOTAL_CHECKS ))
echo -n "Overall Health Score: "

if [ "$HEALTH_SCORE" -ge 90 ]; then
    echo -e "${GREEN}${HEALTH_SCORE}% - HEALTHY${NC}"
    EXIT_CODE=0
elif [ "$HEALTH_SCORE" -ge 70 ]; then
    echo -e "${YELLOW}${HEALTH_SCORE}% - DEGRADED${NC}"
    EXIT_CODE=1
else
    echo -e "${RED}${HEALTH_SCORE}% - CRITICAL${NC}"
    EXIT_CODE=2
fi

# Detailed results
if [ "$FAILED_CHECKS" -gt 0 ] || [ "$WARNINGS" -gt 0 ]; then
    echo ""
    echo "Issues Found:"
    for result in "${RESULTS[@]}"; do
        if [[ "$result" == *"✗"* ]] || [[ "$result" == *"⚠"* ]]; then
            echo "  $result"
        fi
    done
fi

# Write results to file for monitoring
TIMESTAMP=$(date '+%Y-%m-%d_%H-%M-%S')
mkdir -p /home/<USER>/spheroseg/logs/health-checks
cat > "/home/<USER>/spheroseg/logs/health-checks/health-check-${TIMESTAMP}.json" <<EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "total_checks": $TOTAL_CHECKS,
  "passed": $PASSED_CHECKS,
  "failed": $FAILED_CHECKS,
  "warnings": $WARNINGS,
  "health_score": $HEALTH_SCORE,
  "status": "$([ $HEALTH_SCORE -ge 90 ] && echo "healthy" || ([ $HEALTH_SCORE -ge 70 ] && echo "degraded" || echo "critical"))"
}
EOF

exit $EXIT_CODE