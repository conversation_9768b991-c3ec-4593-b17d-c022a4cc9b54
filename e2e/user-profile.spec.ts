import { test, expect } from '@playwright/test';
import { test as baseTest } from './fixtures/base';

// User Profile Management E2E Tests
test.describe('User Profile Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/auth/signin');
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'testuser123');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
  });

  // Profile View Tests
  test.describe('Profile Viewing', () => {
    test('should display user profile information', async ({ page }) => {
      // Navigate to profile
      await page.click('[data-testid="user-menu"]');
      await page.click('text=Profile');
      await page.waitForURL('**/profile');
      
      // Verify profile information is displayed
      await expect(page.locator('text=<EMAIL>')).toBeVisible();
      await expect(page.locator('[data-testid="profile-name"]')).toBeVisible();
      await expect(page.locator('[data-testid="profile-organization"]')).toBeVisible();
      await expect(page.locator('[data-testid="member-since"]')).toBeVisible();
    });

    test('should show storage usage statistics', async ({ page }) => {
      await page.goto('/profile');
      
      // Check storage stats
      await expect(page.locator('[data-testid="storage-used"]')).toBeVisible();
      await expect(page.locator('[data-testid="storage-limit"]')).toBeVisible();
      await expect(page.locator('[data-testid="storage-percentage"]')).toBeVisible();
      
      // Verify progress bar
      const progressBar = page.locator('[role="progressbar"]');
      await expect(progressBar).toBeVisible();
      const ariaValue = await progressBar.getAttribute('aria-valuenow');
      expect(Number(ariaValue)).toBeGreaterThanOrEqual(0);
      expect(Number(ariaValue)).toBeLessThanOrEqual(100);
    });

    test('should display project statistics', async ({ page }) => {
      await page.goto('/profile');
      
      // Check project stats
      await expect(page.locator('[data-testid="total-projects"]')).toBeVisible();
      await expect(page.locator('[data-testid="total-images"]')).toBeVisible();
      await expect(page.locator('[data-testid="total-segmentations"]')).toBeVisible();
    });
  });

  // Profile Editing Tests
  test.describe('Profile Editing', () => {
    test('should allow editing basic profile information', async ({ page }) => {
      await page.goto('/profile');
      await page.click('button:has-text("Edit Profile")');
      
      // Update profile fields
      await page.fill('[name="firstName"]', 'Updated');
      await page.fill('[name="lastName"]', 'User');
      await page.fill('[name="organization"]', 'Test Organization');
      await page.fill('[name="department"]', 'Research');
      
      // Save changes
      await page.click('button:has-text("Save Changes")');
      
      // Verify success message
      await expect(page.locator('text=Profile updated successfully')).toBeVisible();
      
      // Verify changes persisted
      await page.reload();
      await expect(page.locator('[data-testid="profile-name"]')).toContainText('Updated User');
      await expect(page.locator('[data-testid="profile-organization"]')).toContainText('Test Organization');
    });

    test('should validate profile form inputs', async ({ page }) => {
      await page.goto('/profile');
      await page.click('button:has-text("Edit Profile")');
      
      // Try invalid inputs
      await page.fill('[name="firstName"]', '');
      await page.fill('[name="email"]', 'invalid-email');
      
      // Try to save
      await page.click('button:has-text("Save Changes")');
      
      // Should show validation errors
      await expect(page.locator('text=First name is required')).toBeVisible();
      await expect(page.locator('text=Invalid email address')).toBeVisible();
    });

    test('should handle profile picture upload', async ({ page }) => {
      await page.goto('/profile');
      await page.click('button:has-text("Edit Profile")');
      
      // Upload profile picture
      const [fileChooser] = await Promise.all([
        page.waitForEvent('filechooser'),
        page.click('[data-testid="upload-avatar"]')
      ]);
      
      await fileChooser.setFiles({
        name: 'avatar.jpg',
        mimeType: 'image/jpeg',
        buffer: Buffer.from('fake-image-data')
      });
      
      // Wait for upload
      await expect(page.locator('text=Avatar uploaded successfully')).toBeVisible();
      
      // Verify avatar is displayed
      const avatar = page.locator('[data-testid="user-avatar"]');
      await expect(avatar).toBeVisible();
      const src = await avatar.getAttribute('src');
      expect(src).toContain('avatar');
    });
  });

  // Account Settings Tests
  test.describe('Account Settings', () => {
    test('should allow changing password', async ({ page }) => {
      await page.goto('/profile/settings');
      await page.click('text=Security');
      
      // Fill password change form
      await page.fill('[name="currentPassword"]', 'testuser123');
      await page.fill('[name="newPassword"]', 'NewPassword123!');
      await page.fill('[name="confirmPassword"]', 'NewPassword123!');
      
      // Submit
      await page.click('button:has-text("Update Password")');
      
      // Should show success
      await expect(page.locator('text=Password updated successfully')).toBeVisible();
      
      // Should be logged out
      await expect(page).toHaveURL(/.*signin/);
      
      // Verify can login with new password
      await page.fill('[name="email"]', '<EMAIL>');
      await page.fill('[name="password"]', 'NewPassword123!');
      await page.click('button[type="submit"]');
      await expect(page).toHaveURL('**/dashboard');
    });

    test('should validate password requirements', async ({ page }) => {
      await page.goto('/profile/settings');
      await page.click('text=Security');
      
      // Try weak password
      await page.fill('[name="currentPassword"]', 'testuser123');
      await page.fill('[name="newPassword"]', '123');
      await page.fill('[name="confirmPassword"]', '123');
      
      await page.click('button:has-text("Update Password")');
      
      // Should show validation error
      await expect(page.locator('text=Password must be at least 8 characters')).toBeVisible();
    });

    test('should allow enabling two-factor authentication', async ({ page }) => {
      await page.goto('/profile/settings');
      await page.click('text=Security');
      
      // Enable 2FA
      await page.click('button:has-text("Enable Two-Factor Authentication")');
      
      // Should show QR code
      await expect(page.locator('[data-testid="2fa-qr-code"]')).toBeVisible();
      await expect(page.locator('[data-testid="2fa-secret"]')).toBeVisible();
      
      // Enter verification code (would need to mock this)
      await page.fill('[name="verificationCode"]', '123456');
      await page.click('button:has-text("Verify and Enable")');
      
      // Should show success
      await expect(page.locator('text=Two-factor authentication enabled')).toBeVisible();
    });

    test('should manage email preferences', async ({ page }) => {
      await page.goto('/profile/settings');
      await page.click('text=Notifications');
      
      // Toggle email preferences
      const marketingToggle = page.locator('[data-testid="email-marketing"]');
      const securityToggle = page.locator('[data-testid="email-security"]');
      const updatesToggle = page.locator('[data-testid="email-updates"]');
      
      // Verify current state
      await expect(marketingToggle).toBeVisible();
      await expect(securityToggle).toBeVisible();
      await expect(updatesToggle).toBeVisible();
      
      // Toggle settings
      await marketingToggle.click();
      await updatesToggle.click();
      
      // Save
      await page.click('button:has-text("Save Preferences")');
      
      // Verify success
      await expect(page.locator('text=Preferences updated')).toBeVisible();
      
      // Reload and verify persistence
      await page.reload();
      await expect(marketingToggle).not.toBeChecked();
      await expect(securityToggle).toBeChecked();
      await expect(updatesToggle).not.toBeChecked();
    });

    test('should configure API access tokens', async ({ page }) => {
      await page.goto('/profile/settings');
      await page.click('text=API Access');
      
      // Create new API token
      await page.click('button:has-text("Generate New Token")');
      await page.fill('[name="tokenName"]', 'Test API Token');
      await page.selectOption('[name="tokenScope"]', 'read');
      await page.click('button:has-text("Create Token")');
      
      // Should show token
      const tokenElement = page.locator('[data-testid="api-token-value"]');
      await expect(tokenElement).toBeVisible();
      const token = await tokenElement.textContent();
      expect(token).toMatch(/^[A-Za-z0-9_-]+$/);
      
      // Verify token appears in list
      await expect(page.locator('text=Test API Token')).toBeVisible();
      
      // Test revoking token
      await page.click('[data-testid="revoke-token-Test API Token"]');
      await page.click('button:has-text("Confirm Revoke")');
      
      await expect(page.locator('text=Token revoked successfully')).toBeVisible();
      await expect(page.locator('text=Test API Token')).not.toBeVisible();
    });
  });

  // Privacy Settings Tests
  test.describe('Privacy Settings', () => {
    test('should configure profile visibility', async ({ page }) => {
      await page.goto('/profile/settings');
      await page.click('text=Privacy');
      
      // Change profile visibility
      await page.selectOption('[name="profileVisibility"]', 'private');
      await page.click('button:has-text("Save Privacy Settings")');
      
      await expect(page.locator('text=Privacy settings updated')).toBeVisible();
    });

    test('should manage data export requests', async ({ page }) => {
      await page.goto('/profile/settings');
      await page.click('text=Privacy');
      
      // Request data export
      await page.click('button:has-text("Export My Data")');
      await page.click('button:has-text("Confirm Export Request")');
      
      await expect(page.locator('text=Data export requested')).toBeVisible();
      await expect(page.locator('text=You will receive an email when your data is ready')).toBeVisible();
    });

    test('should handle account deletion request', async ({ page }) => {
      await page.goto('/profile/settings');
      await page.click('text=Privacy');
      
      // Scroll to danger zone
      await page.locator('[data-testid="danger-zone"]').scrollIntoViewIfNeeded();
      
      // Click delete account
      await page.click('button:has-text("Delete Account")');
      
      // Should show confirmation dialog
      await expect(page.locator('text=This action cannot be undone')).toBeVisible();
      
      // Enter confirmation
      await page.fill('[name="confirmDelete"]', 'DELETE');
      await page.fill('[name="password"]', 'testuser123');
      
      // Cancel (don't actually delete in test)
      await page.click('button:has-text("Cancel")');
      
      // Verify still logged in
      await expect(page).toHaveURL('**/profile/settings');
    });
  });

  // Theme and Display Settings Tests
  test.describe('Display Settings', () => {
    test('should toggle dark mode', async ({ page }) => {
      await page.goto('/profile/settings');
      await page.click('text=Appearance');
      
      // Get initial theme
      const htmlElement = page.locator('html');
      const initialTheme = await htmlElement.getAttribute('data-theme');
      
      // Toggle theme
      await page.click('[data-testid="theme-toggle"]');
      
      // Verify theme changed
      const newTheme = await htmlElement.getAttribute('data-theme');
      expect(newTheme).not.toBe(initialTheme);
      
      // Verify persistence after reload
      await page.reload();
      const persistedTheme = await htmlElement.getAttribute('data-theme');
      expect(persistedTheme).toBe(newTheme);
    });

    test('should change language preference', async ({ page }) => {
      await page.goto('/profile/settings');
      await page.click('text=Appearance');
      
      // Change language
      await page.selectOption('[name="language"]', 'es');
      await page.click('button:has-text("Save Appearance")');
      
      // Should reload with Spanish
      await page.waitForTimeout(1000);
      
      // Verify some Spanish text appears
      await expect(page.locator('text=Configuración')).toBeVisible();
    });

    test('should configure date/time format', async ({ page }) => {
      await page.goto('/profile/settings');
      await page.click('text=Appearance');
      
      // Change date format
      await page.selectOption('[name="dateFormat"]', 'DD/MM/YYYY');
      await page.selectOption('[name="timeFormat"]', '24h');
      await page.selectOption('[name="timezone"]', 'Europe/Prague');
      
      await page.click('button:has-text("Save Appearance")');
      
      await expect(page.locator('text=Appearance settings updated')).toBeVisible();
    });
  });

  // Session Management Tests
  test.describe('Session Management', () => {
    test('should display active sessions', async ({ page }) => {
      await page.goto('/profile/settings');
      await page.click('text=Sessions');
      
      // Should show current session
      await expect(page.locator('[data-testid="session-list"]')).toBeVisible();
      await expect(page.locator('text=Current Session')).toBeVisible();
      
      // Verify session details
      await expect(page.locator('[data-testid="session-browser"]')).toBeVisible();
      await expect(page.locator('[data-testid="session-ip"]')).toBeVisible();
      await expect(page.locator('[data-testid="session-last-active"]')).toBeVisible();
    });

    test('should allow terminating other sessions', async ({ page }) => {
      await page.goto('/profile/settings');
      await page.click('text=Sessions');
      
      // If there are other sessions, test termination
      const otherSessions = page.locator('[data-testid="other-session"]');
      const count = await otherSessions.count();
      
      if (count > 0) {
        await page.click('[data-testid="terminate-session"]').first();
        await page.click('button:has-text("Confirm Terminate")');
        
        await expect(page.locator('text=Session terminated')).toBeVisible();
      }
      
      // Test "Sign out all other sessions"
      await page.click('button:has-text("Sign Out All Other Sessions")');
      await page.fill('[name="password"]', 'testuser123');
      await page.click('button:has-text("Confirm")');
      
      await expect(page.locator('text=All other sessions terminated')).toBeVisible();
    });
  });
});