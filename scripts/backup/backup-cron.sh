#!/bin/bash
# Cron setup script for automated database backups

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_SCRIPT="$SCRIPT_DIR/backup-production.sh"
CRON_SCHEDULE="0 2 * * *"  # Daily at 2 AM

echo "Setting up automated database backups..."

# Ensure backup script is executable
chmod +x "$BACKUP_SCRIPT"

# Create cron job
(crontab -l 2>/dev/null | grep -v "$BACKUP_SCRIPT" || true; echo "$CRON_SCHEDULE $BACKUP_SCRIPT >> /var/log/spheroseg-backup.log 2>&1") | crontab -

echo "Cron job created. Current crontab:"
crontab -l

echo "Backup schedule: $CRON_SCHEDULE (daily at 2 AM)"
echo "Backup script: $BACKUP_SCRIPT"
echo "Log file: /var/log/spheroseg-backup.log"