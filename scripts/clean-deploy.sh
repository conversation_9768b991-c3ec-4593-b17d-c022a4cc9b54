#!/bin/bash
# Clean deploy script for SpherosegV4 production
# Ensures completely clean build and deployment

set -e

echo "🚀 Starting clean production deployment..."

# 0. Run comprehensive Docker cleanup first
echo "🧹 Running comprehensive Docker cleanup..."
/home/<USER>/spheroseg/scripts/docker-cleanup.sh

# 1. Stop running containers
echo "🛑 Stopping running containers..."
docker-compose --env-file .env.production --profile prod down || true

# 2. Remove ALL old images to force rebuild
echo "🗑️  Removing ALL Docker images..."
docker rmi $(docker images -q) -f 2>/dev/null || true

# 3. Clean Docker system completely
echo "🧹 Deep cleaning Docker system..."
docker system prune -af --volumes

# 4. Clean npm cache on host
echo "📦 Cleaning npm cache..."
npm cache clean --force

# 5. Remove all build artifacts
echo "🗑️  Removing build artifacts..."
rm -rf packages/*/dist
rm -rf packages/frontend/node_modules/.vite
rm -rf packages/frontend/node_modules/.cache
rm -rf .turbo
rm -rf node_modules/.cache

# 6. Build application with clean cache
echo "🔨 Building application..."
npm run build

# 7. Build and start Docker containers
echo "🐳 Building and starting Docker containers..."
docker-compose --env-file .env.production --profile prod build --no-cache frontend-prod
docker-compose --env-file .env.production --profile prod up -d

# 8. Wait for services to be healthy
echo "⏳ Waiting for services to be healthy..."
sleep 10

# 9. Check service status
echo "📊 Checking service status..."
docker-compose --env-file .env.production --profile prod ps

echo "✅ Clean deployment completed!"
echo "🌐 Access the application at: https://spherosegapp.utia.cas.cz"