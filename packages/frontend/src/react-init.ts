// Ensure React is available globally before any other code runs
import * as React from 'react';
import * as ReactDOM from 'react-dom/client';

// Make React and ReactDOM available globally
if (typeof window !== 'undefined') {
  (window as any).React = React;
  (window as any).ReactDOM = ReactDOM;
  
  // Also expose commonly used React functions
  (window as any).createElement = React.createElement;
  (window as any).Component = React.Component;
  (window as any).PureComponent = React.PureComponent;
  (window as any).Fragment = React.Fragment;
  (window as any).useState = React.useState;
  (window as any).useEffect = React.useEffect;
  (window as any).useContext = React.useContext;
  (window as any).useRef = React.useRef;
  (window as any).useMemo = React.useMemo;
  (window as any).useCallback = React.useCallback;
}

// Re-export for normal module usage
export { React, ReactDOM };