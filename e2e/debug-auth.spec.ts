import { test, expect } from '@playwright/test';

test('debug auth page', async ({ page }) => {
  // Navigate to signin page
  await page.goto('/auth/signin');
  
  // Take screenshot
  await page.screenshot({ path: 'auth-page.png', fullPage: true });
  console.log('Screenshot saved to auth-page.png');
  
  // Log page content
  const title = await page.title();
  console.log('Page title:', title);
  
  // Get all input elements
  const inputs = await page.locator('input').all();
  console.log('Number of inputs found:', inputs.length);
  
  // Log each input's attributes
  for (let i = 0; i < inputs.length; i++) {
    const name = await inputs[i].getAttribute('name');
    const type = await inputs[i].getAttribute('type');
    const id = await inputs[i].getAttribute('id');
    console.log(`Input ${i}: name="${name}", type="${type}", id="${id}"`);
  }
  
  // Get page HTML to debug
  const bodyText = await page.locator('body').innerText();
  console.log('Page body text (first 500 chars):', bodyText.substring(0, 500));
});