# Redis Configuration for Production
bind 127.0.0.1 ::1
protected-mode yes
port 6379

# Authentication
requirepass xB4fG7jL9mP2qR5tW8yC

# Persistence
save 900 1
save 300 10
save 60 10000

# Security
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG ""

# Performance
maxmemory 512mb
maxmemory-policy allkeys-lru

# Logging
loglevel notice
logfile /var/log/redis/redis-server.log