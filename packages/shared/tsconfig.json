{"extends": "../../tsconfig.base.json", "compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "lib": ["ES2020", "DOM", "DOM.Iterable"], "declaration": true, "outDir": "dist", "jsx": "react-jsx", "baseUrl": ".", "paths": {"@shared/*": ["src/*"], "@/*": ["src/*"]}, "noEmit": false, "allowImportingTsExtensions": false, "exactOptionalPropertyTypes": false}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts", "src/testing/**/*", "src/services/upload/**/*"]}