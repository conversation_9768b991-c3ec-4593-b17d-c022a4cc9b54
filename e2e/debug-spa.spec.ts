import { test, expect } from '@playwright/test';

test('debug SPA loading', async ({ page }) => {
  // Enable console logging
  page.on('console', msg => console.log('Browser console:', msg.type(), msg.text()));
  page.on('pageerror', error => console.log('Page error:', error.message));
  
  // Navigate to root first
  await page.goto('/');
  console.log('Root URL:', page.url());
  
  // Wait for app to load
  await page.waitForTimeout(2000);
  
  // Check if it's an SPA by looking for React root
  const reactRoot = await page.locator('#root, [id="root"], .app, [class*="app"]').count();
  console.log('React root elements found:', reactRoot);
  
  // Try client-side navigation
  await page.goto('/auth/signin', { waitUntil: 'networkidle' });
  console.log('After navigation URL:', page.url());
  
  // Wait a bit more for client-side rendering
  await page.waitForTimeout(2000);
  
  // Check for inputs again
  const inputs = await page.locator('input').count();
  console.log('Inputs after wait:', inputs);
  
  // Check for any error messages
  const errorElements = await page.locator('[class*="error"], [class*="Error"]').count();
  console.log('Error elements found:', errorElements);
  
  // Get the main content
  const mainContent = await page.locator('main, [role="main"], #root').first();
  if (await mainContent.count() > 0) {
    const text = await mainContent.innerText();
    console.log('Main content:', text.substring(0, 200));
  }
});