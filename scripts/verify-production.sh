#!/bin/bash

# Production Verification Script for SpherosegV4
# This script performs a comprehensive health check of all production services

set -euo pipefail

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=================================${NC}"
echo -e "${BLUE}SpherosegV4 Production Verification${NC}"
echo -e "${BLUE}=================================${NC}"
echo ""

# Function to check service
check_service() {
    local service_name=$1
    local url=$2
    local expected_code=${3:-200}
    
    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "$expected_code"; then
        echo -e "${GREEN}✓${NC} $service_name is accessible"
    else
        echo -e "${RED}✗${NC} $service_name is NOT accessible at $url"
    fi
}

# Function to check Docker container
check_container() {
    local container_name=$1
    local service_desc=$2
    
    if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "$container_name.*Up"; then
        local status=$(docker ps --format "{{.Status}}" --filter "name=$container_name" | head -1)
        echo -e "${GREEN}✓${NC} $service_desc: $status"
    else
        echo -e "${RED}✗${NC} $service_desc is NOT running"
    fi
}

# Check Docker containers
echo -e "${YELLOW}Checking Docker Containers:${NC}"
check_container "spheroseg-nginx-prod" "Nginx Proxy"
check_container "spheroseg-frontend-prod" "Frontend"
check_container "spheroseg-backend" "Backend API"
check_container "spheroseg-db" "PostgreSQL Database"
check_container "spheroseg-redis" "Redis Cache"
check_container "spheroseg-rabbitmq" "RabbitMQ Queue"
check_container "spheroseg-ml" "ML Service"
echo ""

# Check web services
echo -e "${YELLOW}Checking Web Services:${NC}"
check_service "Main Application" "https://spherosegapp.utia.cas.cz" "200"
check_service "API Health" "https://spherosegapp.utia.cas.cz/api/health" "200"
check_service "Backend Direct" "http://localhost:5001/health" "200"
echo ""

# Check monitoring services
echo -e "${YELLOW}Checking Monitoring Stack:${NC}"
check_container "spheroseg-prometheus" "Prometheus"
check_container "spheroseg-grafana" "Grafana"
check_container "spheroseg-node-exporter" "Node Exporter"
check_container "spheroseg-cadvisor" "cAdvisor"
check_service "Prometheus" "http://localhost:9090" "200"
check_service "Grafana" "http://localhost:3003" "200"
echo ""

# Check logging services
echo -e "${YELLOW}Checking Logging Stack:${NC}"
check_container "spheroseg-elasticsearch" "Elasticsearch"
check_container "spheroseg-logstash" "Logstash"
check_container "spheroseg-kibana" "Kibana"
check_container "spheroseg-filebeat" "Filebeat"
check_service "Elasticsearch" "http://localhost:9200" "200"
check_service "Kibana" "http://localhost:5601" "200"
echo ""

# Check SSL certificate
echo -e "${YELLOW}Checking SSL Certificate:${NC}"
if curl -vI https://spherosegapp.utia.cas.cz 2>&1 | grep -q "SSL certificate verify ok"; then
    echo -e "${GREEN}✓${NC} SSL certificate is valid"
    # Get expiry date
    expiry=$(echo | openssl s_client -servername spherosegapp.utia.cas.cz -connect spherosegapp.utia.cas.cz:443 2>/dev/null | openssl x509 -noout -dates 2>/dev/null | grep notAfter | cut -d= -f2)
    if [ ! -z "$expiry" ]; then
        echo -e "  Certificate expires: $expiry"
    fi
else
    echo -e "${RED}✗${NC} SSL certificate issue detected"
fi
echo ""

# Check disk space
echo -e "${YELLOW}Checking Disk Space:${NC}"
df -h | grep -E "^/dev/" | while read line; do
    usage=$(echo $line | awk '{print $5}' | sed 's/%//')
    mount=$(echo $line | awk '{print $6}')
    if [ $usage -gt 90 ]; then
        echo -e "${RED}✗${NC} High disk usage on $mount: ${usage}%"
    elif [ $usage -gt 75 ]; then
        echo -e "${YELLOW}⚠${NC} Warning: $mount is ${usage}% full"
    else
        echo -e "${GREEN}✓${NC} $mount: ${usage}% used"
    fi
done
echo ""

# Check memory usage
echo -e "${YELLOW}Checking Memory Usage:${NC}"
mem_info=$(free -m | grep "^Mem:")
total_mem=$(echo $mem_info | awk '{print $2}')
used_mem=$(echo $mem_info | awk '{print $3}')
mem_percent=$((used_mem * 100 / total_mem))
if [ $mem_percent -gt 90 ]; then
    echo -e "${RED}✗${NC} High memory usage: ${mem_percent}%"
else
    echo -e "${GREEN}✓${NC} Memory usage: ${mem_percent}% (${used_mem}MB / ${total_mem}MB)"
fi
echo ""

# Summary
echo -e "${BLUE}=================================${NC}"
echo -e "${BLUE}Verification Complete${NC}"
echo -e "${BLUE}=================================${NC}"

# Count issues
issues=$(grep -c "✗" /tmp/verify_output 2>/dev/null || echo "0")
warnings=$(grep -c "⚠" /tmp/verify_output 2>/dev/null || echo "0")

if [ "$issues" -eq 0 ]; then
    echo -e "${GREEN}All systems operational!${NC}"
    exit 0
else
    echo -e "${RED}Found $issues issues and $warnings warnings${NC}"
    exit 1
fi