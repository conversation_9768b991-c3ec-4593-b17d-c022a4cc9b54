import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import React from 'react';
import { useImageApi } from '../../hooks/api/useImageApi';
import apiClient from '@/services/api/client';

// Import ImageStatus as a type, not enum
type ImageStatus = 'queued' | 'processing' | 'completed' | 'failed' | 'saving' | 'without_segmentation';

// Mock image data
const mockImages = [
  {
    id: 'image-1',
    projectId: 'project-1',
    filename: 'test1.jpg',
    originalFilename: 'original1.jpg',
    status: 'completed' as ImageStatus,
    width: 800,
    height: 600,
    fileSize: 250000,
    mimeType: 'image/jpeg',
    createdAt: '2023-06-01T12:00:00Z',
    updatedAt: '2023-06-01T12:10:00Z',
  },
  {
    id: 'image-2',
    projectId: 'project-1',
    filename: 'test2.jpg',
    originalFilename: 'original2.jpg',
    status: 'queued' as ImageStatus,
    width: 1024,
    height: 768,
    fileSize: 400000,
    mimeType: 'image/jpeg',
    createdAt: '2023-06-02T12:00:00Z',
    updatedAt: '2023-06-02T12:00:00Z',
  },
];

// Mock file for upload tests
const createMockFile = () => {
  const blob = new Blob(['mock file content'], { type: 'image/jpeg' });
  return new File([blob], 'test-upload.jpg', { type: 'image/jpeg' });
};

// Create a simple wrapper component  
const wrapper = ({ children }: { children: React.ReactNode }) => <>{children}</>;

describe('Image API Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    // Mock FormData since it's not available in the test environment
    global.FormData = class FormData {
      private data = new Map();
      append(key: string, value: unknown) {
        this.data.set(key, value);
      }
      get(key: string) {
        return this.data.get(key);
      }
      // Add other methods as needed
    } as unknown as typeof FormData;
  });

  afterEach(() => {
    vi.restoreAllMocks();
    vi.useRealTimers();
  });

  describe('fetchProjectImages', () => {
    it('should fetch project images successfully', async () => {
      // Configure the mock
      (apiClient.get as any).mockResolvedValueOnce({ 
        data: mockImages,
        status: 200 
      });
      
      const { result } = renderHook(() => useImageApi(), { wrapper });

      let images;
      await act(async () => {
        images = await result.current.fetchProjectImages('project-1');
      });

      expect(apiClient.get).toHaveBeenCalledWith('/projects/project-1/images');
      expect(images).toEqual(mockImages);
    });

    it('should handle project not found error', async () => {
      // Configure the mock to reject
      (apiClient.get as any).mockRejectedValueOnce(new Error('Project not found'));
      
      const { result } = renderHook(() => useImageApi(), { wrapper });

      await act(async () => {
        await expect(result.current.fetchProjectImages('non-existent')).rejects.toThrow('Project not found');
      });

      expect(apiClient.get).toHaveBeenCalledWith('/projects/non-existent/images');
    });

    it('should handle unauthorized access', async () => {
      // Configure the mock to reject with unauthorized error
      (apiClient.get as any).mockRejectedValueOnce(new Error('Unauthorized access'));
      
      const { result } = renderHook(() => useImageApi(), { wrapper });

      await act(async () => {
        await expect(result.current.fetchProjectImages('project-1')).rejects.toThrow('Unauthorized access');
      });
    });
  });

  describe('uploadImage', () => {
    it('should upload an image successfully', async () => {
      const mockFile = createMockFile();
      const uploadedImage = {
        id: 'new-image-id',
        projectId: 'project-1',
        filename: 'new-image.jpg',
        originalFilename: 'test-upload.jpg',
        status: 'queued' as ImageStatus,
        width: 1280,
        height: 720,
        fileSize: 350000,
        mimeType: 'image/jpeg',
        createdAt: '2023-06-10T12:00:00Z',
        updatedAt: '2023-06-10T12:00:00Z',
      };

      // Configure the mock
      (apiClient.post as any).mockResolvedValueOnce({ 
        data: uploadedImage,
        status: 201 
      });

      const { result } = renderHook(() => useImageApi(), { wrapper });

      let response;
      await act(async () => {
        response = await result.current.uploadImage('project-1', mockFile);
      });

      expect(apiClient.post).toHaveBeenCalledWith(
        '/projects/project-1/images', 
        expect.any(FormData),
        expect.objectContaining({
          headers: { 'Content-Type': 'multipart/form-data' },
          onUploadProgress: expect.any(Function)
        })
      );
      expect(response).toEqual(uploadedImage);
    });

    it('should handle invalid file type error', async () => {
      // Create a text file instead of an image
      const blob = new Blob(['text content'], { type: 'text/plain' });
      const invalidFile = new File([blob], 'not-an-image.txt', {
        type: 'text/plain',
      });

      // Configure the mock to reject
      (apiClient.post as any).mockRejectedValueOnce(new Error('Invalid file type. Only images are allowed.'));

      const { result } = renderHook(() => useImageApi(), { wrapper });

      await act(async () => {
        await expect(result.current.uploadImage('project-1', invalidFile)).rejects.toThrow('Invalid file type');
      });
    });

    it('should handle file size limit exceeded', async () => {
      const mockFile = createMockFile();

      // Configure the mock to reject
      (apiClient.post as any).mockRejectedValueOnce(new Error('File size exceeds the limit of 10MB'));

      const { result } = renderHook(() => useImageApi(), { wrapper });

      await act(async () => {
        await expect(result.current.uploadImage('project-1', mockFile)).rejects.toThrow('File size exceeds the limit');
      });
    });

    it('should handle upload progress events', async () => {
      const mockFile = createMockFile();
      const uploadedImage = {
        id: 'new-image-id',
        projectId: 'project-1',
        filename: 'new-image.jpg',
        originalFilename: 'test-upload.jpg',
        status: 'queued' as ImageStatus,
        width: 1280,
        height: 720,
        fileSize: 350000,
        mimeType: 'image/jpeg',
        createdAt: '2023-06-10T12:00:00Z',
        updatedAt: '2023-06-10T12:00:00Z',
      };

      const progressCallback = vi.fn();

      // Configure the mock with a custom implementation to simulate progress
      (apiClient.post as any).mockImplementationOnce(async (_url: string, _data: any, options: any) => {
        // Simulate progress events
        if (options?.onUploadProgress) {
          await vi.advanceTimersByTimeAsync(100);
          options.onUploadProgress({ loaded: 100000, total: 350000 });
          await vi.advanceTimersByTimeAsync(100);
          options.onUploadProgress({ loaded: 200000, total: 350000 });
          await vi.advanceTimersByTimeAsync(100);
          options.onUploadProgress({ loaded: 350000, total: 350000 });
        }
        return { data: uploadedImage, status: 201 };
      });

      const { result } = renderHook(() => useImageApi(), { wrapper });

      let response;
      await act(async () => {
        response = await result.current.uploadImage('project-1', mockFile, progressCallback);
      });

      expect(response).toEqual(uploadedImage);
      expect(progressCallback).toHaveBeenCalledTimes(3);
      expect(progressCallback).toHaveBeenCalledWith(expect.objectContaining({ progress: expect.any(Number) }));
      // Check progress values
      expect(progressCallback).toHaveBeenNthCalledWith(1, { progress: 29 }); // Math.round(100000 / 350000 * 100)
      expect(progressCallback).toHaveBeenNthCalledWith(2, { progress: 57 }); // Math.round(200000 / 350000 * 100)  
      expect(progressCallback).toHaveBeenNthCalledWith(3, { progress: 100 }); // Math.round(350000 / 350000 * 100)
    });
  });

  describe('deleteImage', () => {
    it('should delete an image successfully', async () => {
      // Configure the mock
      (apiClient.delete as any).mockResolvedValueOnce({ 
        data: { success: true },
        status: 200 
      });

      const { result } = renderHook(() => useImageApi(), { wrapper });

      let response;
      await act(async () => {
        response = await result.current.deleteImage('project-1', 'image-1');
      });

      expect(apiClient.delete).toHaveBeenCalledWith('/projects/project-1/images/image-1');
      expect(response.success).toBe(true);
    });

    it('should handle image not found error', async () => {
      // Configure the mock to reject
      (apiClient.delete as any).mockRejectedValueOnce(new Error('Image not found'));

      const { result } = renderHook(() => useImageApi(), { wrapper });

      await act(async () => {
        await expect(result.current.deleteImage('project-1', 'non-existent')).rejects.toThrow('Image not found');
      });
    });

    it('should handle unauthorized deletion', async () => {
      // Configure the mock to reject
      (apiClient.delete as any).mockRejectedValueOnce(new Error('Unauthorized to delete this image'));

      const { result } = renderHook(() => useImageApi(), { wrapper });

      await act(async () => {
        await expect(result.current.deleteImage('project-1', 'image-1')).rejects.toThrow(
          'Unauthorized to delete this image',
        );
      });
    });
  });

  describe('getImageDetails', () => {
    it('should fetch image details successfully', async () => {
      const imageDetails = {
        ...mockImages[0],
        metadata: {
          location: { lat: 40.7128, lng: -74.006 },
          captureDate: '2023-05-30T10:15:00Z',
          camera: 'iPhone 13 Pro',
        },
      };

      // Configure the mock
      (apiClient.get as any).mockResolvedValueOnce({ 
        data: imageDetails,
        status: 200 
      });

      const { result } = renderHook(() => useImageApi(), { wrapper });

      let details;
      await act(async () => {
        details = await result.current.getImageDetails('project-1', 'image-1');
      });

      expect(apiClient.get).toHaveBeenCalledWith('/projects/project-1/images/image-1');
      expect(details).toEqual(imageDetails);
    });

    it('should handle image not found error', async () => {
      // Configure the mock to reject
      (apiClient.get as any).mockRejectedValueOnce(new Error('Image not found'));

      const { result } = renderHook(() => useImageApi(), { wrapper });

      await act(async () => {
        await expect(result.current.getImageDetails('project-1', 'non-existent')).rejects.toThrow('Image not found');
      });
    });
  });

  describe('updateImageStatus', () => {
    it('should update image status successfully', async () => {
      const updatedImage = {
        ...mockImages[1],
        status: 'completed' as ImageStatus,
        updatedAt: '2023-06-10T15:30:00Z',
      };

      // Configure the mock
      (apiClient.patch as any).mockResolvedValueOnce({ 
        data: updatedImage,
        status: 200 
      });

      const { result } = renderHook(() => useImageApi(), { wrapper });

      let image;
      await act(async () => {
        image = await result.current.updateImageStatus('project-1', 'image-2', 'completed');
      });

      expect(apiClient.patch).toHaveBeenCalledWith('/projects/project-1/images/image-2', { status: 'completed' });
      expect(image).toEqual(updatedImage);
    });

    it('should handle invalid status value', async () => {
      // Configure the mock to reject
      (apiClient.patch as any).mockRejectedValueOnce(new Error('Invalid status value'));

      const { result } = renderHook(() => useImageApi(), { wrapper });

      await act(async () => {
        // @ts-expect-error - Intentionally passing invalid status
        await expect(result.current.updateImageStatus('project-1', 'image-1', 'invalid_status')).rejects.toThrow(
          'Invalid status value',
        );
      });
    });
  });
});
