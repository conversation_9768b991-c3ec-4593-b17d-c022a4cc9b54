import { test, expect } from '@playwright/test';

test.describe('Production Health Check', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to production site
    await page.goto('https://spherosegapp.utia.cas.cz');
  });

  test('should load without JavaScript errors', async ({ page }) => {
    // Set up console error monitoring
    const consoleErrors: string[] = [];
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    // Wait for page to fully load
    await page.waitForLoadState('networkidle');
    
    // Check for any JavaScript initialization errors
    const initErrors = consoleErrors.filter(error => 
      error.includes('Cannot access') && error.includes('before initialization')
    );
    
    // Assert no initialization errors
    expect(initErrors).toHaveLength(0);
    
    // Check page is not blank
    const rootElement = await page.$('#root');
    expect(rootElement).not.toBeNull();
    
    // Check if React app mounted
    const reactContent = await page.textContent('body');
    expect(reactContent).not.toBe('');
  });

  test('should render login page correctly', async ({ page }) => {
    // Wait for login form
    await page.waitForSelector('form', { timeout: 10000 });
    
    // Check for email input
    const emailInput = await page.$('input[name="email"], input[type="email"]');
    expect(emailInput).not.toBeNull();
    
    // Check for password input
    const passwordInput = await page.$('input[name="password"], input[type="password"]');
    expect(passwordInput).not.toBeNull();
    
    // Check for submit button
    const submitButton = await page.$('button[type="submit"]');
    expect(submitButton).not.toBeNull();
  });

  test('should have working navigation', async ({ page }) => {
    // Check for navigation links
    const navLinks = await page.$$('nav a, header a');
    expect(navLinks.length).toBeGreaterThan(0);
  });

  test('should load vendor bundles without errors', async ({ page }) => {
    // Get all script tags
    const scripts = await page.$$eval('script[src]', elements => 
      elements.map(el => el.getAttribute('src'))
    );
    
    // Filter vendor scripts
    const vendorScripts = scripts.filter(src => src && src.includes('vendor'));
    expect(vendorScripts.length).toBeGreaterThan(0);
    
    // Check each vendor script loads successfully
    for (const script of vendorScripts) {
      if (script) {
        const response = await page.request.get(script);
        expect(response.status()).toBe(200);
      }
    }
  });

  test('should have responsive design', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500);
    
    const mobileContent = await page.$('body');
    expect(mobileContent).not.toBeNull();
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.waitForTimeout(500);
    
    const desktopContent = await page.$('body');
    expect(desktopContent).not.toBeNull();
  });

  test('should have proper CSP and security headers', async ({ page }) => {
    const response = await page.goto('https://spherosegapp.utia.cas.cz');
    
    if (response) {
      const headers = response.headers();
      
      // Check for security headers
      expect(headers['x-frame-options'] || headers['content-security-policy']).toBeDefined();
    }
  });
});

test.describe('Production API Health', () => {
  test('should have working API endpoints', async ({ request }) => {
    // Test health endpoint
    const healthResponse = await request.get('https://spherosegapp.utia.cas.cz/api/health');
    expect(healthResponse.ok()).toBe(true);
    
    const healthData = await healthResponse.json();
    expect(healthData).toHaveProperty('status');
  });

  test('should handle CORS properly', async ({ request }) => {
    const response = await request.get('https://spherosegapp.utia.cas.cz/api/health', {
      headers: {
        'Origin': 'https://spherosegapp.utia.cas.cz'
      }
    });
    
    expect(response.ok()).toBe(true);
  });
});