#!/bin/bash
# Clean build script for SpherosegV4
# Ensures no cache pollution between builds

set -e

echo "🧹 Starting clean build process..."

# 1. Clean npm cache
echo "📦 Cleaning npm cache..."
npm cache clean --force

# 2. Remove node_modules in all packages
echo "🗑️  Removing node_modules..."
rm -rf node_modules
rm -rf packages/*/node_modules
rm -rf packages/frontend/node_modules/.vite
rm -rf packages/frontend/node_modules/.cache

# 3. Remove all dist directories
echo "🗑️  Removing dist directories..."
rm -rf packages/*/dist
rm -rf packages/frontend/dist

# 4. Remove Turborepo cache
echo "🗑️  Removing Turborepo cache..."
rm -rf .turbo
rm -rf node_modules/.cache

# 5. Remove Vite cache
echo "🗑️  Removing Vite cache..."
rm -rf packages/frontend/.vite-cache
rm -rf packages/frontend/node_modules/.vite

# 6. Clean package-lock
echo "🔄 Removing package-lock.json files..."
rm -f package-lock.json
rm -f packages/*/package-lock.json

# 7. Fresh install
echo "📥 Installing dependencies..."
npm install

# 8. Build
echo "🔨 Building application..."
npm run build

echo "✅ Clean build completed!"