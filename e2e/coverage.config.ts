import { PlaywrightTestConfig } from '@playwright/test';
import * as path from 'path';

// E2E Coverage Configuration
export const coverageConfig = {
  // Coverage collection settings
  enabled: process.env.COLLECT_COVERAGE === 'true',
  
  // Coverage thresholds
  thresholds: {
    global: {
      features: 85,      // % of features tested
      interactions: 80,  // % of user interactions covered
      errors: 90,        // % of error scenarios tested
      integration: 85,   // % of integrations tested
    },
    perFile: {
      features: 75,
      interactions: 70,
      errors: 85,
      integration: 80,
    },
    critical: {
      // Critical paths must have 100% coverage
      authentication: 100,
      payments: 100,
      dataIntegrity: 100,
      security: 100,
    }
  },

  // Test categories and their weights
  categories: {
    security: {
      weight: 0.25,
      critical: true,
      tests: [
        'security.spec.ts'
      ]
    },
    userManagement: {
      weight: 0.15,
      critical: true,
      tests: [
        'user-profile.spec.ts',
        'auth/*.spec.ts'
      ]
    },
    integration: {
      weight: 0.20,
      critical: true,
      tests: [
        'integration.spec.ts'
      ]
    },
    resilience: {
      weight: 0.15,
      critical: false,
      tests: [
        'resilience.spec.ts'
      ]
    },
    dataManagement: {
      weight: 0.10,
      critical: true,
      tests: [
        'data-management.spec.ts'
      ]
    },
    features: {
      weight: 0.10,
      critical: false,
      tests: [
        'advanced-features.spec.ts',
        'features/*.spec.ts'
      ]
    },
    collaboration: {
      weight: 0.05,
      critical: false,
      tests: [
        'collaboration.spec.ts'
      ]
    }
  },

  // Feature mapping for coverage calculation
  featureMap: {
    authentication: {
      tests: ['auth/login.spec.ts', 'auth/signup.spec.ts', 'auth/logout.spec.ts'],
      weight: 10,
      critical: true
    },
    projects: {
      tests: ['projects/*.spec.ts'],
      weight: 8,
      critical: true
    },
    imageUpload: {
      tests: ['upload/*.spec.ts'],
      weight: 7,
      critical: true
    },
    segmentation: {
      tests: ['segmentation/*.spec.ts'],
      weight: 9,
      critical: true
    },
    export: {
      tests: ['export/*.spec.ts'],
      weight: 6,
      critical: false
    },
    search: {
      tests: ['search/*.spec.ts'],
      weight: 5,
      critical: false
    },
    sharing: {
      tests: ['sharing/*.spec.ts', 'collaboration.spec.ts'],
      weight: 4,
      critical: false
    },
    profile: {
      tests: ['user-profile.spec.ts'],
      weight: 6,
      critical: true
    },
    security: {
      tests: ['security.spec.ts'],
      weight: 10,
      critical: true
    },
    gdpr: {
      tests: ['data-management.spec.ts'],
      weight: 8,
      critical: true
    }
  },

  // Interaction patterns to track
  interactions: {
    clicks: ['button', 'a', '[role="button"]', '[data-testid*="clickable"]'],
    inputs: ['input', 'textarea', 'select', '[contenteditable]'],
    navigation: ['page.goto', 'page.goBack', 'page.goForward'],
    uploads: ['filechooser', 'setInputFiles'],
    downloads: ['download', 'page.pdf'],
    api: ['page.request', 'route', 'response'],
    assertions: ['expect', 'toBeVisible', 'toHaveText', 'toHaveValue']
  },

  // Report settings
  reports: {
    html: {
      outputDir: 'coverage-report',
      openAfterGeneration: true
    },
    json: {
      outputFile: 'coverage-report/coverage.json'
    },
    console: {
      verbose: true,
      showUncovered: true
    },
    junit: {
      outputFile: 'coverage-report/junit.xml'
    },
    markdown: {
      outputFile: 'coverage-report/COVERAGE.md',
      includeBadges: true
    }
  },

  // Custom metrics
  customMetrics: {
    browserCoverage: {
      calculate: (results: any) => {
        const browsers = ['chromium', 'firefox', 'webkit', 'mobile-chrome'];
        const tested = browsers.filter(b => results.browsers?.includes(b)).length;
        return (tested / browsers.length) * 100;
      }
    },
    errorCoverage: {
      calculate: (results: any) => {
        const errorScenarios = results.tests.filter((t: any) => 
          t.title.toLowerCase().includes('error') ||
          t.title.toLowerCase().includes('fail') ||
          t.title.toLowerCase().includes('invalid')
        );
        return (errorScenarios.length / results.tests.length) * 100;
      }
    },
    performanceCoverage: {
      calculate: (results: any) => {
        const performanceTests = results.tests.filter((t: any) =>
          t.title.toLowerCase().includes('performance') ||
          t.title.toLowerCase().includes('load') ||
          t.title.toLowerCase().includes('speed')
        );
        return performanceTests.length > 0 ? 100 : 0;
      }
    }
  }
};

// Coverage collection helpers
export const coverageHelpers = {
  // Track feature usage
  trackFeature: (featureName: string) => {
    if (global.__coverage__) {
      global.__coverage__.features = global.__coverage__.features || {};
      global.__coverage__.features[featureName] = true;
    }
  },

  // Track interaction
  trackInteraction: (type: string, selector: string) => {
    if (global.__coverage__) {
      global.__coverage__.interactions = global.__coverage__.interactions || [];
      global.__coverage__.interactions.push({ type, selector, timestamp: Date.now() });
    }
  },

  // Track error scenario
  trackError: (scenario: string, handled: boolean) => {
    if (global.__coverage__) {
      global.__coverage__.errors = global.__coverage__.errors || [];
      global.__coverage__.errors.push({ scenario, handled, timestamp: Date.now() });
    }
  },

  // Calculate coverage score
  calculateCoverage: (results: any) => {
    const weights = coverageConfig.categories;
    let totalScore = 0;
    let totalWeight = 0;

    Object.entries(weights).forEach(([category, config]) => {
      const categoryTests = results.tests.filter((t: any) => 
        config.tests.some(pattern => t.file.includes(pattern.replace('*.spec.ts', '')))
      );
      
      const passed = categoryTests.filter((t: any) => t.status === 'passed').length;
      const total = categoryTests.length;
      const score = total > 0 ? (passed / total) * 100 : 0;
      
      totalScore += score * config.weight;
      totalWeight += config.weight;
    });

    return totalWeight > 0 ? totalScore / totalWeight : 0;
  }
};

// Export configuration for use in tests
declare global {
  interface Window {
    __coverage__: any;
  }
  var __coverage__: any;
}

export default coverageConfig;