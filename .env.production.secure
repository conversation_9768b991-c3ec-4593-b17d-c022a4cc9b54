# Production Environment Variables for SpherosegV4
# Generated: 2025-01-23
# IMPORTANT: This file contains sensitive information. Keep it secure!

NODE_ENV=production
APP_NAME=SpherosegV4
APP_URL=https://spherosegapp.utia.cas.cz

# Backend API
API_PORT=5001
API_URL=http://localhost:5001

# Database (MUST CHANGE DEFAULT CREDENTIALS!)
DATABASE_URL=********************************************************/spheroseg_prod?sslmode=disable
DB_SSL=false
DB_PASSWORD=jK9mN3pQ7rS2xV5wY8zA
POSTGRES_USER=spheroseg_prod
POSTGRES_PASSWORD=jK9mN3pQ7rS2xV5wY8zA
POSTGRES_DB=spheroseg_prod

# Redis (with password protection)
REDIS_URL=redis://default:xB4fG7jL9mP2qR5tW8yC@redis:6379
REDIS_PASSWORD=xB4fG7jL9mP2qR5tW8yC

# RabbitMQ (secure credentials)
RABBITMQ_DEFAULT_USER=spheroseg_rmq
RABBITMQ_DEFAULT_PASS=aD3gH6kM9pS2vX5zB8nC
RABBITMQ_USER=spheroseg_rmq
RABBITMQ_PASS=aD3gH6kM9pS2vX5zB8nC
RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672
RABBITMQ_QUEUE=segmentation_tasks

# JWT & Security (cryptographically secure secrets)
JWT_SECRET=x5gqRas0nfJyPPwvyM455egKkM2veyxhHEMV6CzLFM4jrnjqBSVj78NuuM6UD5pEsfio5JWK4paClxQX6XezZQ==
JWT_REFRESH_SECRET=tge8kbZ4AhBCoDgT8xDxC22QBbGokvvm1I0LoVaaLheV8gyEoLrfLQTC8SG38HwM9BB42wxQ/x5o0bLthBaXdA==
SESSION_SECRET=uA3RICMWdcX/+FriJ0Y08FxG3daJ5QKShkJEeTXPu+N9Df/3PK1eltj5/QVIA24l
CSRF_SECRET=vLb8RQlJR+iOgZno5vxMFbYCbBfyOPvX/wHgkACRuNFjj2vioN/Ju/oKb4vStl0v

# Email Configuration
EMAIL_FROM=<EMAIL>
EMAIL_HOST=mail.utia.cas.cz
EMAIL_PORT=25
EMAIL_USER=<EMAIL>
EMAIL_PASS=CHANGE_THIS_EMAIL_PASSWORD

# CORS (production domains only)
ALLOWED_ORIGINS=https://spherosegapp.utia.cas.cz

# SSL/HTTPS
SECURE_COOKIES=true
FORCE_HTTPS=true
REQUIRE_HTTPS=true
HSTS_MAX_AGE=31536000

# Performance & Monitoring
ENABLE_PERFORMANCE_MONITORING=true
LOG_LEVEL=info
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
ENABLE_RATE_LIMIT=true
CONTAINER_MEMORY_LIMIT_MB=1024

# Security Features
ENABLE_SECURITY_HEADERS=true
CSP_REPORT_URI=https://spherosegapp.utia.cas.cz/api/csp-report
ENABLE_CSRF_PROTECTION=true

# Monitoring & Logging
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
ELASTIC_PORT=9200
KIBANA_PORT=5601

# Backup Configuration
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=spheroseg-backups
BACKUP_S3_REGION=eu-west-1

# Feature Flags
ENABLE_ML_SERVICE=true
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_WEBSOCKET=true
ENABLE_EXPORT_FEATURES=true

# Frontend Configuration
VITE_API_URL=https://spherosegapp.utia.cas.cz
VITE_API_BASE_URL=/api
VITE_ASSETS_URL=https://spherosegapp.utia.cas.cz/assets
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_TRACKING=true