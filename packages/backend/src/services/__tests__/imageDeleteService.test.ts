/**
 * Integration tests for ImageDeleteService
 * 
 * Demonstrates proper database transaction handling for tests
 * using the new testDatabase utilities
 * 
 * Tests include:
 * - Single image deletion with transactions
 * - Batch deletion with partial rollback
 * - Permission validation
 * - Transaction rollback on errors
 * - File cleanup
 * - Storage quota updates
 */

import { jest } from '@jest/globals';
import { PoolClient } from 'pg';

// Mock environment variables before any imports
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test';
process.env.JWT_SECRET = 'test-secret';
process.env.REDIS_URL = 'redis://localhost:6379';

// Mock all dependencies before imports
const mockClient = {
  query: jest.fn(),
  release: jest.fn(),
};

const mockPool = {
  connect: jest.fn().mockResolvedValue(mockClient),
  query: jest.fn(),
};

// Create projectService mock
const mockGetProjectById = jest.fn();
const mockProjectService = {
  getProjectById: mockGetProjectById,
  createProject: jest.fn(),
  getUserProjects: jest.fn(),
  updateProject: jest.fn(),
  deleteProject: jest.fn(),
};

// Mock modules with factory functions
jest.mock('../../db', () => ({
  getPool: () => mockPool
}));

// Mock the loader instead of the service directly
jest.mock('../projectServiceLoader', () => {
  console.log('projectServiceLoader mock called');
  return {
    loadProjectService: jest.fn(() => {
      console.log('loadProjectService called, returning:', mockProjectService);
      return Promise.resolve(mockProjectService);
    })
  };
});

// Mock dotenv
jest.mock('dotenv', () => ({
  config: jest.fn(),
}));

// Mock environment validation
jest.mock('../../utils/envValidation', () => ({
  validateEnv: () => ({
    NODE_ENV: 'test',
    DATABASE_URL: 'postgresql://test:test@localhost:5432/test',
    JWT_SECRET: 'test-secret',
    REDIS_URL: 'redis://localhost:6379',
  }),
}));

// Mock secrets loader
jest.mock('../../utils/secretsLoader', () => ({
  loadSecrets: () => ({}),
  constructDatabaseUrl: () => 'postgresql://test:test@localhost:5432/test',
  constructRedisUrl: () => 'redis://localhost:6379',
  constructRabbitmqUrl: () => 'amqp://localhost:5672',
}));

jest.mock('../../utils/logger', () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn((msg, data) => {
      console.log('Logger error:', msg, data);
    }),
  }
}));

jest.mock('../cacheService', () => ({
  default: {
    invalidateImageList: jest.fn().mockResolvedValue(undefined),
    invalidateProject: jest.fn().mockResolvedValue(undefined),
    delPattern: jest.fn().mockResolvedValue(undefined),
    getCachedProject: jest.fn().mockResolvedValue(null),
    cacheProject: jest.fn().mockResolvedValue(undefined),
    get: jest.fn().mockResolvedValue(null),
    set: jest.fn().mockResolvedValue(undefined)
  }
}));

jest.mock('../../utils/imageUtils.unified', () => ({
  default: {
    dbPathToFilesystemPath: jest.fn((dbPath: string, uploadDir: string) => {
      return `${uploadDir}/${dbPath}`;
    }),
    deleteFile: jest.fn().mockResolvedValue(undefined),
    getFilesInDirectory: jest.fn().mockResolvedValue([])
  }
}));

jest.mock('fs', () => ({
  rmdirSync: jest.fn(() => undefined),
  existsSync: jest.fn(() => false),
  mkdirSync: jest.fn(() => undefined),
}));

jest.mock('fs/promises', () => ({
  mkdir: jest.fn().mockResolvedValue(undefined),
}));

jest.mock('../fileCleanupService', () => ({
  default: {
    cleanupProjectFiles: jest.fn().mockResolvedValue({
      success: true,
      deletedFiles: [],
      failedFiles: []
    })
  }
}));

jest.mock('../../config', () => ({
  __esModule: true,
  default: {
    storage: {
      uploadDir: '/test/uploads',
    },
    db: {
      host: 'localhost',
      port: 5432,
      database: 'test',
      user: 'test',
      password: 'test',
      ssl: false,
    },
    redis: {
      host: 'localhost',
      port: 6379,
    },
    auth: {
      jwtSecret: 'test-secret',
    },
  },
}));

// Import the functions directly from the service
import imageDeleteService, { deleteImage, deleteMultipleImages } from '../imageDeleteService';

describe('ImageDeleteService Integration Tests', () => {
  let mockCacheService: jest.Mocked<typeof import('../cacheService').default>;
  let mockImageUtils: jest.Mocked<typeof import('../../utils/imageUtils.unified').default>;
  let mockLogger: jest.Mocked<typeof import('../../utils/logger').default>;
  let mockFs: jest.Mocked<typeof import('fs')>;

  beforeAll(() => {
    // Get references to mocked modules
    mockCacheService = require('../cacheService').default as any;
    mockImageUtils = require('../../utils/imageUtils.unified').default as any;
    mockLogger = require('../../utils/logger').default as any;
    mockFs = require('fs') as any;
  });

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
    
    // Reset mock implementations
    mockImageUtils.deleteFile.mockResolvedValue(undefined);
    mockImageUtils.getFilesInDirectory.mockResolvedValue([]);
    mockCacheService.invalidateImageList.mockResolvedValue(undefined);
    
    // Reset pool mock for projectService
    mockPool.query.mockReset();
    
    // Reset client mock
    mockClient.query.mockReset();
    mockClient.release.mockReset();
    
    // Reset pool mock
    mockPool.connect.mockResolvedValue(mockClient);
  });

  describe('deleteImage', () => {
    const mockImageId = 'test-image-123';
    const mockProjectId = 'test-project-456';
    const mockUserId = 'test-user-789';

    beforeEach(() => {
      // Mock projectService.getProjectById
      mockGetProjectById.mockResolvedValue({
        id: mockProjectId,
        title: 'Test Project',
        description: null,
        user_id: mockUserId,
        created_at: new Date(),
        updated_at: new Date(),
        is_owner: true,
        permission: 'owner'
      });

      mockClient.query.mockImplementation((query: string) => {
        if (query.includes('SELECT i.id, i.storage_path')) {
          return {
            rows: [{
              id: mockImageId,
              storage_path: 'projects/test/image.jpg',
              thumbnail_path: 'projects/test/thumb.jpg',
              file_size: '1000000',
            }],
          };
        }
        if (query.includes('information_schema.columns')) {
          return { rows: [{ column_name: 'storage_used_bytes' }] };
        }
        if (query === 'BEGIN' || query === 'COMMIT' || query === 'ROLLBACK') {
          return {};
        }
        if (query.includes('DELETE FROM segmentation_results')) {
          return {};
        }
        if (query.includes('DELETE FROM images')) {
          return {};
        }
        if (query.includes('UPDATE users')) {
          return {};
        }
        return { rows: [] };
      });

      mockImageUtils.dbPathToFilesystemPath.mockImplementation((path) => `/test/uploads/${path}`);
      mockImageUtils.deleteFile.mockResolvedValue(undefined);
      mockImageUtils.getFilesInDirectory.mockResolvedValue([]);
      mockCacheService.invalidateImageList.mockResolvedValue(undefined);
    });

    it('should delete an image successfully with owner permission', async () => {
      const result = await deleteImage(mockImageId, mockProjectId, mockUserId);
      
      expect(result).toEqual({
        imageId: mockImageId,
        success: true,
      });

      // Verify transaction flow
      expect(mockClient.query).toHaveBeenCalledWith('BEGIN');
      expect(mockClient.query).toHaveBeenCalledWith(
        'DELETE FROM segmentation_results WHERE image_id = $1',
        [mockImageId]
      );
      expect(mockClient.query).toHaveBeenCalledWith(
        'DELETE FROM images WHERE id = $1',
        [mockImageId]
      );
      expect(mockClient.query).toHaveBeenCalledWith('COMMIT');

      // Verify file deletion
      expect(mockImageUtils.deleteFile).toHaveBeenCalledTimes(2);
      expect(mockImageUtils.deleteFile).toHaveBeenCalledWith('/test/uploads/projects/test/image.jpg');
      expect(mockImageUtils.deleteFile).toHaveBeenCalledWith('/test/uploads/projects/test/thumb.jpg');

      // Verify cache invalidation
      expect(mockCacheService.invalidateImageList).toHaveBeenCalledWith(mockProjectId);
    });

    it('should delete an image with edit permission', async () => {
      // Mock project shared with edit permission
      mockGetProjectById.mockResolvedValue({
        id: mockProjectId,
        title: 'Test Project',
        description: null,
        user_id: 'other-user',
        created_at: new Date(),
        updated_at: new Date(),
        is_owner: false,
        permission: 'edit'
      });

      const result = await deleteImage(mockImageId, mockProjectId, mockUserId);

      expect(result.success).toBe(true);
    });

    it('should fail with view-only permission', async () => {
      // Mock project shared with view permission only
      mockGetProjectById.mockResolvedValue({
        id: mockProjectId,
        title: 'Test Project',
        description: null,
        user_id: 'other-user',
        created_at: new Date(),
        updated_at: new Date(),
        is_owner: false,
        permission: 'view'
      });

      const result = await deleteImage(mockImageId, mockProjectId, mockUserId);

      expect(result.success).toBe(false);
      expect(result.error).toContain('permission');
    });

    it('should handle project not found', async () => {
      // Mock no project found
      mockGetProjectById.mockResolvedValue(null);

      const result = await deleteImage(mockImageId, mockProjectId, mockUserId);

      expect(result.success).toBe(false);
      expect(result.error).toContain('not found');
    });

    it('should handle image not found', async () => {
      mockClient.query.mockImplementation((query: string) => {
        if (query.includes('SELECT i.id, i.storage_path')) {
          return { rows: [] };
        }
        return { rows: [] };
      });

      const result = await deleteImage(mockImageId, mockProjectId, mockUserId);

      expect(result.success).toBe(false);
      expect(result.error).toContain('not found');
    });

    it('should rollback transaction on error', async () => {
      mockClient.query.mockImplementation((query: string) => {
        if (query.includes('DELETE FROM images')) {
          throw new Error('Database error');
        }
        if (query.includes('SELECT i.id, i.storage_path')) {
          return {
            rows: [{
              id: mockImageId,
              storage_path: 'projects/test/image.jpg',
              thumbnail_path: 'projects/test/thumb.jpg',
              file_size: '1000000',
            }],
          };
        }
        return { rows: [] };
      });

      const result = await deleteImage(mockImageId, mockProjectId, mockUserId);

      expect(result.success).toBe(false);
      expect(mockClient.query).toHaveBeenCalledWith('ROLLBACK');
      expect(mockImageUtils.deleteFile).not.toHaveBeenCalled();
    });

    it('should update user storage quota', async () => {
      await deleteImage(mockImageId, mockProjectId, mockUserId);

      expect(mockClient.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE users SET storage_used_bytes'),
        ['1000000', mockUserId]
      );
    });

    it('should handle missing storage_used_bytes column gracefully', async () => {
      mockClient.query.mockImplementation((query: string) => {
        if (query.includes('information_schema.columns')) {
          return { rows: [] }; // Column doesn't exist
        }
        if (query.includes('SELECT i.id, i.storage_path')) {
          return {
            rows: [{
              id: mockImageId,
              storage_path: 'projects/test/image.jpg',
              thumbnail_path: 'projects/test/thumb.jpg',
              file_size: '1000000',
            }],
          };
        }
        if (query === 'BEGIN' || query === 'COMMIT' || query === 'ROLLBACK') {
          return {};
        }
        if (query.includes('DELETE FROM')) {
          return {};
        }
        return { rows: [] };
      });

      const result = await deleteImage(mockImageId, mockProjectId, mockUserId);

      expect(result.success).toBe(true);
      expect(mockClient.query).not.toHaveBeenCalledWith(
        expect.stringContaining('UPDATE users SET storage_used_bytes'),
        expect.any(Array)
      );
    });

    it('should continue even if file deletion fails', async () => {
      mockImageUtils.deleteFile.mockRejectedValue(new Error('File not found'));

      const result = await deleteImage(mockImageId, mockProjectId, mockUserId);

      expect(result.success).toBe(true);
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Error deleting image file',
        expect.any(Object)
      );
    });

    it('should clean up empty project directory', async () => {
      const result = await deleteImage(mockImageId, mockProjectId, mockUserId);

      expect(result.success).toBe(true);
      expect(mockImageUtils.getFilesInDirectory).toHaveBeenCalled();
      expect(mockFs.rmdirSync).toHaveBeenCalled();
    });

    it('should handle project- prefix in projectId', async () => {
      const result = await deleteImage(mockImageId, `project-${mockProjectId}`, mockUserId);

      expect(result.success).toBe(true);
      // Verify getProjectById was called with the cleaned project ID (prefix removed)
      expect(mockGetProjectById).toHaveBeenCalledWith(
        mockPool,
        mockProjectId,
        mockUserId
      );
    });
  });

  describe('deleteMultipleImages', () => {
    const mockImageIds = ['image-1', 'image-2', 'image-3'];
    const mockProjectId = 'test-project-456';
    const mockUserId = 'test-user-789';

    beforeEach(() => {
      // Mock successful project access
      mockGetProjectById.mockResolvedValue({
        id: mockProjectId,
        title: 'Test Project',
        description: null,
        user_id: mockUserId,
        created_at: new Date(),
        updated_at: new Date(),
        is_owner: true,
        permission: 'owner'
      });

      // Mock successful image queries
      mockClient.query.mockImplementation((query: string, params?: any[]) => {
        if (query.includes('SELECT i.id, i.storage_path')) {
          const imageId = params?.[0];
          return {
            rows: [{
              id: imageId,
              storage_path: `projects/test/${imageId}.jpg`,
              thumbnail_path: `projects/test/${imageId}_thumb.jpg`,
              file_size: '1000000',
            }],
          };
        }
        return { rows: [] };
      });

      mockImageUtils.deleteFile.mockResolvedValue(undefined);
      mockCacheService.invalidateImageList.mockResolvedValue(undefined);
    });

    it('should delete multiple images successfully', async () => {
      const results = await deleteMultipleImages(mockImageIds, mockProjectId, mockUserId);

      expect(results).toHaveLength(3);
      expect(results.every(r => r.success)).toBe(true);
      expect(results.map(r => r.imageId)).toEqual(mockImageIds);
    });

    it('should continue processing if one image fails', async () => {
      mockClient.query.mockImplementation((query: string, params?: any[]) => {
        if (query.includes('SELECT i.id, i.storage_path')) {
          const imageId = params?.[0];
          if (imageId === 'image-2') {
            return { rows: [] }; // Image not found
          }
          return {
            rows: [{
              id: imageId,
              storage_path: `projects/test/${imageId}.jpg`,
              thumbnail_path: `projects/test/${imageId}_thumb.jpg`,
              file_size: '1000000',
            }],
          };
        }
        return { rows: [] };
      });

      const results = await deleteMultipleImages(mockImageIds, mockProjectId, mockUserId);

      expect(results).toHaveLength(3);
      expect(results[0].success).toBe(true);
      expect(results[1].success).toBe(false);
      expect(results[1].error).toBeDefined();
      expect(results[2].success).toBe(true);
    });
  });

  describe('imageDeleteService.canDeleteImage', () => {
    const mockImageId = 'test-image-123';
    const mockProjectId = 'test-project-456';
    const mockUserId = 'test-user-789';

    it('should return true if user can delete image', async () => {
      mockPool.query.mockResolvedValue({
        rows: [{ id: mockImageId }],
      });

      const result = await imageDeleteService.canDeleteImage(mockImageId, mockProjectId, mockUserId);

      expect(result).toBe(true);
      expect(mockPool.query).toHaveBeenCalledWith(
        expect.stringContaining('JOIN projects p ON i.project_id = p.id'),
        [mockImageId, mockProjectId, mockUserId]
      );
    });

    it('should return false if user cannot delete image', async () => {
      mockPool.query.mockResolvedValue({
        rows: [],
      });

      const result = await imageDeleteService.canDeleteImage(mockImageId, mockProjectId, mockUserId);

      expect(result).toBe(false);
    });

    it('should handle database errors gracefully', async () => {
      mockPool.query.mockRejectedValue(new Error('Database error'));

      const result = await imageDeleteService.canDeleteImage(mockImageId, mockProjectId, mockUserId);

      expect(result).toBe(false);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error checking if image can be deleted',
        expect.any(Object)
      );
    });
  });
});