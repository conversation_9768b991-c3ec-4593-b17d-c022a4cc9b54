import { test, expect } from '@playwright/test';
import { test as baseTest } from './fixtures/base';

// Security E2E Tests - Comprehensive security testing suite
test.describe('Security Tests', () => {
  // XSS Prevention Tests
  test.describe('XSS Prevention', () => {
    test('should prevent script injection in project names', async ({ page }) => {
      await page.goto('/auth/signin');
      
      // Login first
      await page.fill('[name="email"]', '<EMAIL>');
      await page.fill('[name="password"]', 'testuser123');
      await page.click('button[type="submit"]');
      await page.waitForURL('**/dashboard');
      
      // Try to create project with XSS payload
      await page.click('button:has-text("Create New Project")');
      const xssPayload = '<script>alert("XSS")</script>';
      await page.fill('[name="name"]', xssPayload);
      await page.fill('[name="description"]', 'Test description');
      await page.click('button:has-text("Create Project")');
      
      // Verify script is escaped in the UI
      const projectName = await page.locator('.project-name').first().textContent();
      expect(projectName).not.toContain('<script>');
      expect(projectName).toContain('&lt;script&gt;');
      
      // Verify no alert was triggered
      await expect(page.locator('.alert')).not.toBeVisible();
    });

    test('should sanitize HTML in descriptions', async ({ page }) => {
      await page.goto('/auth/signin');
      await page.fill('[name="email"]', '<EMAIL>');
      await page.fill('[name="password"]', 'testuser123');
      await page.click('button[type="submit"]');
      await page.waitForURL('**/dashboard');
      
      // Create project with HTML payload
      await page.click('button:has-text("Create New Project")');
      await page.fill('[name="name"]', 'Security Test Project');
      await page.fill('[name="description"]', '<img src=x onerror=alert("XSS")>');
      await page.click('button:has-text("Create Project")');
      
      // Verify HTML is sanitized
      const description = await page.locator('.project-description').first().innerHTML();
      expect(description).not.toContain('onerror');
      expect(description).not.toContain('<img');
    });

    test('should prevent XSS in URL parameters', async ({ page }) => {
      // Try to inject script via URL parameter
      await page.goto('/projects?search=<script>alert("XSS")</script>');
      
      // Verify no script execution
      await expect(page.locator('.alert')).not.toBeVisible();
      
      // Verify parameter is properly encoded in UI
      const searchInput = await page.locator('input[name="search"]').inputValue();
      expect(searchInput).not.toContain('<script>');
    });
  });

  // SQL Injection Prevention Tests
  test.describe('SQL Injection Prevention', () => {
    test('should prevent SQL injection in login', async ({ page }) => {
      await page.goto('/auth/signin');
      
      // Try SQL injection in email field
      await page.fill('[name="email"]', "admin' OR '1'='1");
      await page.fill('[name="password"]', "' OR '1'='1");
      await page.click('button[type="submit"]');
      
      // Should show invalid credentials error
      await expect(page.locator('text=Invalid email or password')).toBeVisible();
      
      // Should not be logged in
      expect(page.url()).toContain('/auth/signin');
    });

    test('should prevent SQL injection in search', async ({ page }) => {
      // Login first
      await page.goto('/auth/signin');
      await page.fill('[name="email"]', '<EMAIL>');
      await page.fill('[name="password"]', 'testuser123');
      await page.click('button[type="submit"]');
      await page.waitForURL('**/dashboard');
      
      // Try SQL injection in search
      await page.fill('input[name="search"]', "'; DROP TABLE projects; --");
      await page.press('input[name="search"]', 'Enter');
      
      // Should return no results or error, not crash
      await expect(page.locator('text=No projects found')).toBeVisible();
      
      // Verify projects still exist
      await page.reload();
      await expect(page.locator('.project-card')).toBeVisible();
    });
  });

  // CSRF Protection Tests
  test.describe('CSRF Protection', () => {
    test('should require CSRF token for state-changing operations', async ({ page, context }) => {
      // Login
      await page.goto('/auth/signin');
      await page.fill('[name="email"]', '<EMAIL>');
      await page.fill('[name="password"]', 'testuser123');
      await page.click('button[type="submit"]');
      await page.waitForURL('**/dashboard');
      
      // Get auth token
      const cookies = await context.cookies();
      const authToken = cookies.find(c => c.name === 'auth-token')?.value;
      
      // Try to delete project without CSRF token
      const response = await page.request.delete('/api/projects/1', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          // Intentionally omit CSRF token
        }
      });
      
      // Should be rejected
      expect(response.status()).toBe(403);
      const body = await response.json();
      expect(body.error).toContain('CSRF');
    });

    test('should accept valid CSRF token', async ({ page }) => {
      // Login and get CSRF token
      await page.goto('/auth/signin');
      await page.fill('[name="email"]', '<EMAIL>');
      await page.fill('[name="password"]', 'testuser123');
      await page.click('button[type="submit"]');
      await page.waitForURL('**/dashboard');
      
      // Get CSRF token from meta tag or cookie
      const csrfToken = await page.locator('meta[name="csrf-token"]').getAttribute('content');
      expect(csrfToken).toBeTruthy();
      
      // Verify operations work with CSRF token
      await page.click('button:has-text("Create New Project")');
      await page.fill('[name="name"]', 'CSRF Test Project');
      await page.click('button:has-text("Create Project")');
      
      // Should succeed
      await expect(page.locator('text=Project created successfully')).toBeVisible();
    });
  });

  // Authentication Security Tests
  test.describe('Authentication Security', () => {
    test('should enforce password complexity requirements', async ({ page }) => {
      await page.goto('/auth/signup');
      
      // Try weak password
      await page.fill('[name="email"]', '<EMAIL>');
      await page.fill('[name="password"]', '123');
      await page.fill('[name="confirmPassword"]', '123');
      await page.click('button[type="submit"]');
      
      // Should show password requirements error
      await expect(page.locator('text=Password must be at least 8 characters')).toBeVisible();
    });

    test('should implement rate limiting on login attempts', async ({ page }) => {
      await page.goto('/auth/signin');
      
      // Make multiple failed login attempts
      for (let i = 0; i < 6; i++) {
        await page.fill('[name="email"]', '<EMAIL>');
        await page.fill('[name="password"]', 'wrongpassword');
        await page.click('button[type="submit"]');
        await page.waitForTimeout(100);
      }
      
      // Should show rate limit error
      await expect(page.locator('text=Too many login attempts')).toBeVisible();
    });

    test('should expire sessions after inactivity', async ({ page, context }) => {
      // Login
      await page.goto('/auth/signin');
      await page.fill('[name="email"]', '<EMAIL>');
      await page.fill('[name="password"]', 'testuser123');
      await page.click('button[type="submit"]');
      await page.waitForURL('**/dashboard');
      
      // Simulate session expiry by manipulating cookie
      await context.addCookies([{
        name: 'auth-token',
        value: 'expired-token',
        domain: 'localhost',
        path: '/'
      }]);
      
      // Try to access protected page
      await page.goto('/projects');
      
      // Should redirect to login
      await expect(page).toHaveURL(/.*signin/);
    });

    test('should prevent session fixation attacks', async ({ page, context }) => {
      // Get initial session ID
      await page.goto('/');
      const cookiesBefore = await context.cookies();
      const sessionIdBefore = cookiesBefore.find(c => c.name === 'session-id')?.value;
      
      // Login
      await page.goto('/auth/signin');
      await page.fill('[name="email"]', '<EMAIL>');
      await page.fill('[name="password"]', 'testuser123');
      await page.click('button[type="submit"]');
      await page.waitForURL('**/dashboard');
      
      // Session ID should change after login
      const cookiesAfter = await context.cookies();
      const sessionIdAfter = cookiesAfter.find(c => c.name === 'session-id')?.value;
      
      expect(sessionIdAfter).not.toBe(sessionIdBefore);
    });
  });

  // Authorization Tests
  test.describe('Authorization', () => {
    test('should prevent unauthorized access to other users projects', async ({ page }) => {
      // Login as testuser
      await page.goto('/auth/signin');
      await page.fill('[name="email"]', '<EMAIL>');
      await page.fill('[name="password"]', 'testuser123');
      await page.click('button[type="submit"]');
      await page.waitForURL('**/dashboard');
      
      // Try to access another user's project directly
      await page.goto('/projects/999999'); // Non-existent or other user's project
      
      // Should show 404 or unauthorized
      await expect(page.locator('text=Project not found')).toBeVisible();
    });

    test('should validate permissions for shared projects', async ({ page }) => {
      // This would test view-only vs edit permissions
      // Implementation depends on sharing feature
    });
  });

  // Input Validation Tests
  test.describe('Input Validation', () => {
    test('should validate file upload types', async ({ page }) => {
      await page.goto('/auth/signin');
      await page.fill('[name="email"]', '<EMAIL>');
      await page.fill('[name="password"]', 'testuser123');
      await page.click('button[type="submit"]');
      await page.waitForURL('**/dashboard');
      
      // Navigate to project
      await page.click('.project-card').first();
      
      // Try to upload non-image file
      const [fileChooser] = await Promise.all([
        page.waitForEvent('filechooser'),
        page.click('button:has-text("Upload Images")')
      ]);
      
      // Create a fake executable file
      await fileChooser.setFiles({
        name: 'malicious.exe',
        mimeType: 'application/x-executable',
        buffer: Buffer.from('MZ') // PE header
      });
      
      // Should show error
      await expect(page.locator('text=Invalid file type')).toBeVisible();
    });

    test('should enforce file size limits', async ({ page }) => {
      await page.goto('/auth/signin');
      await page.fill('[name="email"]', '<EMAIL>');
      await page.fill('[name="password"]', 'testuser123');
      await page.click('button[type="submit"]');
      await page.waitForURL('**/dashboard');
      
      // Navigate to project
      await page.click('.project-card').first();
      
      // Try to upload oversized file
      const [fileChooser] = await Promise.all([
        page.waitForEvent('filechooser'),
        page.click('button:has-text("Upload Images")')
      ]);
      
      // Create large buffer (e.g., 100MB)
      const largeBuffer = Buffer.alloc(100 * 1024 * 1024);
      
      await fileChooser.setFiles({
        name: 'large-image.jpg',
        mimeType: 'image/jpeg',
        buffer: largeBuffer
      });
      
      // Should show file size error
      await expect(page.locator('text=File size exceeds limit')).toBeVisible();
    });
  });

  // Security Headers Tests
  test.describe('Security Headers', () => {
    test('should set proper security headers', async ({ page }) => {
      const response = await page.goto('/');
      const headers = response?.headers() || {};
      
      // Check security headers
      expect(headers['x-content-type-options']).toBe('nosniff');
      expect(headers['x-frame-options']).toBe('DENY');
      expect(headers['x-xss-protection']).toBe('1; mode=block');
      expect(headers['strict-transport-security']).toContain('max-age=');
      expect(headers['content-security-policy']).toBeTruthy();
    });

    test('should implement CSP correctly', async ({ page }) => {
      // Navigate to page
      await page.goto('/');
      
      // Try to inject inline script
      await page.evaluate(() => {
        const script = document.createElement('script');
        script.textContent = 'window.injected = true;';
        document.head.appendChild(script);
      });
      
      // Script should be blocked by CSP
      const injected = await page.evaluate(() => window.injected);
      expect(injected).toBeUndefined();
    });
  });

  // API Security Tests
  test.describe('API Security', () => {
    test('should not expose sensitive data in API responses', async ({ page }) => {
      // Login
      await page.goto('/auth/signin');
      await page.fill('[name="email"]', '<EMAIL>');
      await page.fill('[name="password"]', 'testuser123');
      await page.click('button[type="submit"]');
      await page.waitForURL('**/dashboard');
      
      // Intercept API response
      const response = await page.request.get('/api/users/profile');
      const userData = await response.json();
      
      // Should not contain sensitive fields
      expect(userData.password).toBeUndefined();
      expect(userData.passwordHash).toBeUndefined();
      expect(userData.refreshToken).toBeUndefined();
      expect(userData.sessionSecret).toBeUndefined();
    });

    test('should validate API input parameters', async ({ page }) => {
      await page.goto('/auth/signin');
      await page.fill('[name="email"]', '<EMAIL>');
      await page.fill('[name="password"]', 'testuser123');
      await page.click('button[type="submit"]');
      await page.waitForURL('**/dashboard');
      
      // Try invalid API parameters
      const response = await page.request.get('/api/projects', {
        params: {
          limit: 'invalid',
          offset: -1,
          sort: '"; DROP TABLE projects; --'
        }
      });
      
      // Should return validation error
      expect(response.status()).toBe(400);
      const body = await response.json();
      expect(body.error).toContain('Invalid parameters');
    });
  });
});