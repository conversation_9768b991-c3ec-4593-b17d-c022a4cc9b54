import { test, expect } from '@playwright/test';

test('should connect to application', async ({ page }) => {
  console.log('Navigating to:', page.context().browser()!.contexts()[0]._options.baseURL || 'http://localhost');
  
  // Try to navigate with explicit handling
  const response = await page.goto('/', {
    waitUntil: 'domcontentloaded',
    timeout: 10000
  });
  
  console.log('Response status:', response?.status());
  console.log('Response URL:', response?.url());
  
  // Check if we get any response
  expect(response).toBeTruthy();
  expect(response!.status()).toBeLessThan(400);
});