#!/bin/bash
# Setup Cron Jobs for SpherosegV4
# Configures automated backups, SSL renewal, and health monitoring

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Log functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Configuration
PROJECT_DIR="/home/<USER>/spheroseg"
SCRIPTS_DIR="$PROJECT_DIR/scripts"
BACKUP_SCRIPT="$SCRIPTS_DIR/backup/backup-database.sh"
SSL_RENEW_SCRIPT="$SCRIPTS_DIR/ssl/renew-certificates.sh"
HEALTH_CHECK_SCRIPT="$SCRIPTS_DIR/health-check.sh"

log_info "Setting up cron jobs for SpherosegV4..."

# Function to add cron job if it doesn't exist
add_cron_job() {
    local schedule=$1
    local command=$2
    local description=$3
    
    # Check if cron job already exists
    if crontab -l 2>/dev/null | grep -q "$command"; then
        log_warn "Cron job already exists: $description"
    else
        # Add the cron job
        (crontab -l 2>/dev/null || true; echo "# $description"; echo "$schedule $command") | crontab -
        log_info "Added cron job: $description"
    fi
}

# 1. Database Backup (Daily at 2 AM)
if [ -f "$BACKUP_SCRIPT" ]; then
    add_cron_job \
        "0 2 * * *" \
        "$BACKUP_SCRIPT >> $PROJECT_DIR/logs/backup.log 2>&1" \
        "Daily database backup at 2 AM"
else
    log_error "Backup script not found: $BACKUP_SCRIPT"
fi

# 2. SSL Certificate Renewal (Daily at 3 AM)
if [ -f "$SSL_RENEW_SCRIPT" ]; then
    add_cron_job \
        "0 3 * * *" \
        "$SSL_RENEW_SCRIPT >> $PROJECT_DIR/logs/ssl-renewal.log 2>&1" \
        "Daily SSL certificate renewal check at 3 AM"
else
    log_warn "SSL renewal script not found: $SSL_RENEW_SCRIPT"
fi

# 3. Health Check (Every 5 minutes)
if [ -f "$HEALTH_CHECK_SCRIPT" ]; then
    add_cron_job \
        "*/5 * * * *" \
        "$HEALTH_CHECK_SCRIPT >> $PROJECT_DIR/logs/health-check.log 2>&1" \
        "Health check every 5 minutes"
else
    log_error "Health check script not found: $HEALTH_CHECK_SCRIPT"
fi

# 4. Log Rotation (Weekly on Sunday at 1 AM)
add_cron_job \
    "0 1 * * 0" \
    "find $PROJECT_DIR/logs -name '*.log' -mtime +30 -delete" \
    "Weekly log rotation - delete logs older than 30 days"

# 5. Disk Space Alert (Daily at 6 AM)
add_cron_job \
    "0 6 * * *" \
    "df -h / | awk 'NR==2 {if(\$5+0 > 80) print \"ALERT: Disk usage is \"\$5}' | mail -s 'SpherosegV4 Disk Alert' <EMAIL> || true" \
    "Daily disk space check at 6 AM"

# 6. Docker System Cleanup (Weekly on Sunday at 2 AM)
add_cron_job \
    "0 2 * * 0" \
    "docker system prune -af --volumes >> $PROJECT_DIR/logs/docker-cleanup.log 2>&1" \
    "Weekly Docker cleanup - remove unused images and volumes"

# Create log directory if it doesn't exist
mkdir -p "$PROJECT_DIR/logs"

# Create systemd service for health monitoring (optional)
log_info "Creating systemd service for continuous health monitoring..."
sudo tee /etc/systemd/system/spheroseg-health-monitor.service > /dev/null <<EOF
[Unit]
Description=SpherosegV4 Health Monitor
After=docker.service
Requires=docker.service

[Service]
Type=simple
ExecStart=$SCRIPTS_DIR/health-monitor.sh
Restart=always
RestartSec=10
User=$USER
WorkingDirectory=$PROJECT_DIR
StandardOutput=append:$PROJECT_DIR/logs/health-monitor-service.log
StandardError=append:$PROJECT_DIR/logs/health-monitor-service.log

[Install]
WantedBy=multi-user.target
EOF

# Enable and start the health monitor service
if sudo systemctl daemon-reload && sudo systemctl enable spheroseg-health-monitor.service; then
    log_info "Health monitor service enabled"
    echo "Start it with: sudo systemctl start spheroseg-health-monitor"
else
    log_warn "Failed to enable health monitor service"
fi

# Display current crontab
echo ""
log_info "Current cron jobs:"
crontab -l 2>/dev/null | grep -E "(spheroseg|backup|health|ssl)" || echo "No SpherosegV4 cron jobs found"

echo ""
log_info "Cron job setup completed!"
echo ""
echo "Next steps:"
echo "1. Verify cron jobs with: crontab -l"
echo "2. Check cron logs with: tail -f /var/log/cron"
echo "3. Start health monitor service: sudo systemctl start spheroseg-health-monitor"
echo "4. Monitor application logs in: $PROJECT_DIR/logs/"