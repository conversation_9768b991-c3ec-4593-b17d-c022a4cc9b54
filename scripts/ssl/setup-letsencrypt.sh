#!/bin/bash
# Let's Encrypt SSL Certificate Setup Script for SpherosegV4

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="spherosegapp.utia.cas.cz"
EMAIL="<EMAIL>"
WEBROOT="/var/www/letsencrypt"

# Log functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   log_error "This script must be run as root or with sudo"
   exit 1
fi

log_info "Setting up Let's Encrypt SSL certificates for ${DOMAIN}..."

# Create required directories
log_info "Creating required directories..."
mkdir -p ./letsencrypt/etc/letsencrypt
mkdir -p ./letsencrypt/var/lib/letsencrypt
mkdir -p ./letsencrypt/webroot/.well-known/acme-challenge
mkdir -p ./ssl

# Create nginx configuration for Let's Encrypt verification
log_info "Creating temporary nginx configuration for Let's Encrypt..."
cat > nginx.letsencrypt.conf <<'EOF'
server {
    listen 80;
    server_name spherosegapp.utia.cas.cz www.spherosegapp.utia.cas.cz;

    location /.well-known/acme-challenge/ {
        root /var/www/letsencrypt;
        allow all;
    }

    location / {
        return 301 https://$server_name$request_uri;
    }
}
EOF

# Stop existing nginx if running
log_info "Stopping existing nginx container..."
docker-compose --profile prod stop nginx-prod || true

# Start temporary nginx for Let's Encrypt verification
log_info "Starting temporary nginx for Let's Encrypt verification..."
docker run -d \
    --name nginx-letsencrypt \
    -p 80:80 \
    -v $(pwd)/nginx.letsencrypt.conf:/etc/nginx/conf.d/default.conf \
    -v $(pwd)/letsencrypt/webroot:/var/www/letsencrypt \
    nginx:alpine

# Wait for nginx to start
sleep 5

# Run certbot
log_info "Running certbot to obtain SSL certificates..."
docker run --rm \
    -v $(pwd)/letsencrypt/etc/letsencrypt:/etc/letsencrypt \
    -v $(pwd)/letsencrypt/var/lib/letsencrypt:/var/lib/letsencrypt \
    -v $(pwd)/letsencrypt/webroot:/var/www/letsencrypt \
    certbot/certbot certonly \
    --webroot \
    --webroot-path=/var/www/letsencrypt \
    --email ${EMAIL} \
    --agree-tos \
    --no-eff-email \
    --force-renewal \
    -d ${DOMAIN} \
    -d www.${DOMAIN}

# Check if certificates were obtained successfully
if [ -f "./letsencrypt/etc/letsencrypt/live/${DOMAIN}/fullchain.pem" ]; then
    log_info "SSL certificates obtained successfully!"
    
    # Copy certificates to ssl directory
    cp ./letsencrypt/etc/letsencrypt/live/${DOMAIN}/fullchain.pem ./ssl/
    cp ./letsencrypt/etc/letsencrypt/live/${DOMAIN}/privkey.pem ./ssl/
    
    log_info "Certificates copied to ./ssl/ directory"
else
    log_error "Failed to obtain SSL certificates"
    docker stop nginx-letsencrypt || true
    docker rm nginx-letsencrypt || true
    exit 1
fi

# Stop temporary nginx
log_info "Stopping temporary nginx..."
docker stop nginx-letsencrypt
docker rm nginx-letsencrypt

# Update nginx production configuration to use Let's Encrypt certificates
log_info "Updating nginx production configuration..."
cat > nginx.prod.ssl.conf <<'EOF'
# HTTP to HTTPS redirect
server {
    listen 80;
    server_name spherosegapp.utia.cas.cz www.spherosegapp.utia.cas.cz;
    
    location /.well-known/acme-challenge/ {
        root /var/www/letsencrypt;
        allow all;
    }
    
    location / {
        return 301 https://$server_name$request_uri;
    }
}

# HTTPS server
server {
    listen 443 ssl http2;
    server_name spherosegapp.utia.cas.cz www.spherosegapp.utia.cas.cz;

    # SSL certificates from Let's Encrypt
    ssl_certificate /etc/letsencrypt/live/spherosegapp.utia.cas.cz/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/spherosegapp.utia.cas.cz/privkey.pem;

    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # HSTS
    add_header Strict-Transport-Security "max-age=63072000" always;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Frontend
    location / {
        proxy_pass http://frontend:80;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # API
    location /api/ {
        proxy_pass http://backend:5001/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Socket.IO
    location /socket.io/ {
        proxy_pass http://backend:5001/socket.io/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Static assets
    location /uploads/ {
        proxy_pass http://assets:80/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# Create auto-renewal script
log_info "Creating auto-renewal script..."
cat > ./scripts/ssl/renew-certificates.sh <<'EOF'
#!/bin/bash
# Auto-renewal script for Let's Encrypt certificates

docker run --rm \
    -v $(pwd)/letsencrypt/etc/letsencrypt:/etc/letsencrypt \
    -v $(pwd)/letsencrypt/var/lib/letsencrypt:/var/lib/letsencrypt \
    -v $(pwd)/letsencrypt/webroot:/var/www/letsencrypt \
    certbot/certbot renew --quiet

# Reload nginx if renewal was successful
if [ $? -eq 0 ]; then
    docker-compose --profile prod exec nginx-prod nginx -s reload
fi
EOF

chmod +x ./scripts/ssl/renew-certificates.sh

# Set up cron job for auto-renewal
log_info "Setting up auto-renewal cron job..."
(crontab -l 2>/dev/null; echo "0 3 * * * $(pwd)/scripts/ssl/renew-certificates.sh") | crontab -

log_info "Let's Encrypt SSL setup completed successfully!"
log_info ""
log_info "Next steps:"
log_info "1. Update docker-compose to use nginx.prod.ssl.conf"
log_info "2. Restart nginx with: docker-compose --profile prod restart nginx-prod"
log_info "3. Test SSL at: https://www.ssllabs.com/ssltest/analyze.html?d=${DOMAIN}"
log_info ""
log_info "Auto-renewal is configured to run daily at 3 AM"