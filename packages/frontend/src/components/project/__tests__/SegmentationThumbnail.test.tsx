import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import SegmentationThumbnail from '../SegmentationThumbnail';
import apiClient from '@/services/api/client';
import * as svgUtils from '@/lib/svgUtils';

// Mock the API client
vi.mock('@/services/api/client', () => ({
  default: {
    get: vi.fn(),
  },
}));

// Mock the SVG utils
vi.mock('@/lib/svgUtils', async () => {
  const actual = await vi.importActual('@/lib/svgUtils');
  return {
    ...actual,
    scalePolygons: vi.fn().mockImplementation((polygons) => polygons),
  };
});

describe('SegmentationThumbnail Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the image without segmentation when no data is available', async () => {
    // Mock API response with no polygons
    (apiClient.get as vi.Mock).mockResolvedValue({
      data: {
        polygons: [],
      },
    });

    render(
      <SegmentationThumbnail
        imageId="test-image-id"
        projectId="test-project-id"
        thumbnailUrl="https://example.com/image.jpg"
        altText="Test Image"
        width={300}
        height={300}
      />,
    );

    // Check if the image is rendered
    const image = screen.getByAltText('Test Image');
    expect(image).toBeInTheDocument();

    // Wait for API call to complete - the component tries multiple endpoints
    await waitFor(() => {
      expect(apiClient.get).toHaveBeenCalled();
    });

    // No SVG should be rendered since there are no polygons
    const svg = document.querySelector('svg');
    expect(svg).not.toBeInTheDocument();
  });

  it('renders the image with segmentation overlay when data is available', async () => {
    // Mock API response with polygons
    (apiClient.get as vi.Mock).mockResolvedValue({
      data: {
        polygons: [
          {
            id: 'polygon-1',
            points: [
              { x: 10, y: 10 },
              { x: 100, y: 10 },
              { x: 100, y: 100 },
              { x: 10, y: 100 },
            ],
            type: 'external',
            color: '#ff0000',
          },
        ],
        imageWidth: 200,
        imageHeight: 200,
      },
    });

    // Mock the scalePolygons function to return the same polygons
    (svgUtils.scalePolygons as vi.Mock).mockImplementation((polygons) => {
      return polygons;
    });

    // Mock container dimensions
    Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
      configurable: true,
      value: 300,
    });
    Object.defineProperty(HTMLElement.prototype, 'offsetHeight', {
      configurable: true,
      value: 300,
    });

    render(
      <SegmentationThumbnail
        imageId="test-image-id"
        projectId="test-project-id"
        thumbnailUrl="https://example.com/image.jpg"
        altText="Test Image"
        width={300}
        height={300}
      />,
    );

    // Check if the image is rendered
    const image = screen.getByAltText('Test Image') as HTMLImageElement;
    expect(image).toBeInTheDocument();

    // Trigger image load event
    Object.defineProperty(image, 'naturalWidth', {
      value: 200,
      configurable: true,
    });
    Object.defineProperty(image, 'naturalHeight', {
      value: 200,
      configurable: true,
    });
    
    // Fire the load event to trigger the component's onLoad handler
    fireEvent.load(image);

    // Wait for API call to complete and SVG to be rendered
    await waitFor(() => {
      const svg = document.querySelector('svg');
      expect(svg).toBeInTheDocument();

      // Check if path is rendered (not polygon)
      const path = document.querySelector('path');
      expect(path).toBeInTheDocument();
      // The component sets stroke color based on type, external is red
      expect(path).toHaveAttribute('stroke', '#EF4444');
      expect(path).toHaveAttribute('fill', 'transparent');
    });

    // Verify that scalePolygons was called
    await waitFor(() => {
      expect(svgUtils.scalePolygons).toHaveBeenCalled();
    });
  });

  it('handles image loading errors gracefully', async () => {
    // Mock API response
    (apiClient.get as vi.Mock).mockResolvedValue({
      data: {
        polygons: [],
      },
    });

    // Mock the environment variable for API URL
    const originalEnv = import.meta.env.VITE_API_URL;
    import.meta.env.VITE_API_URL = 'http://localhost:5001';

    const { rerender } = render(
      <SegmentationThumbnail
        imageId="test-image-id"
        projectId="test-project-id"
        thumbnailUrl="uploads/invalid-image.jpg"
        fallbackSrc="/placeholder.svg"
        altText="Test Image"
        width={300}
        height={300}
      />,
    );

    // Get the image
    const image = screen.getByAltText('Test Image') as HTMLImageElement;
    
    // Initial URL should be the thumbnailUrl (relative path)
    expect(image.src).toContain('uploads/invalid-image.jpg');

    // Simulate an error loading the image
    fireEvent.error(image);
    
    // Wait a bit for the component to process the error
    await waitFor(() => {
      // After error, the component should fall back to the placeholder
      // The direct URL construction logic requires specific conditions
      expect(image.src).toContain('placeholder.svg');
    });

    // Restore the original env
    import.meta.env.VITE_API_URL = originalEnv;
  });

  it('handles API errors gracefully', async () => {
    // Mock API error
    (apiClient.get as vi.Mock).mockRejectedValue(new Error('API error'));

    render(
      <SegmentationThumbnail
        imageId="test-image-id"
        projectId="test-project-id"
        thumbnailUrl="https://example.com/image.jpg"
        altText="Test Image"
        width={300}
        height={300}
      />,
    );

    // Check if the image is rendered despite API error
    const image = screen.getByAltText('Test Image');
    expect(image).toBeInTheDocument();

    // Wait for API call to complete - the component tries multiple endpoints
    await waitFor(() => {
      expect(apiClient.get).toHaveBeenCalled();
    });

    // No SVG should be rendered since there was an error
    const svg = document.querySelector('svg');
    expect(svg).not.toBeInTheDocument();
  });
});
