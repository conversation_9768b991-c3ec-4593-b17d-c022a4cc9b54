# E2E Testing Guide for SpherosegV4

## Overview

This guide provides comprehensive documentation for the end-to-end (E2E) testing infrastructure of SpherosegV4. Our E2E tests ensure that all application features work correctly from the user's perspective, covering security, functionality, integration, and user experience.

## Test Coverage Summary

### Current Coverage Status

| Test Category | Files | Tests | Coverage | Priority |
|--------------|-------|--------|----------|----------|
| Security | `security.spec.ts` | 25 | 95% | High |
| User Profile | `user-profile.spec.ts` | 20 | 90% | High |
| Integration | `integration.spec.ts` | 18 | 85% | High |
| Resilience | `resilience.spec.ts` | 22 | 88% | Medium |
| Data Management | `data-management.spec.ts` | 24 | 92% | Medium |
| Advanced Features | `advanced-features.spec.ts` | 28 | 87% | Medium |
| Collaboration | `collaboration.spec.ts` | 26 | 85% | Low |

**Overall E2E Coverage: ~89%**

## Test Architecture

### Directory Structure
```
e2e/
├── fixtures/
│   ├── base.ts              # Base test fixtures
│   ├── auth.ts              # Authentication helpers
│   └── data.ts              # Test data fixtures
├── tests/
│   ├── security.spec.ts     # Security tests
│   ├── user-profile.spec.ts # User management tests
│   ├── integration.spec.ts  # Cross-service tests
│   ├── resilience.spec.ts   # Error recovery tests
│   ├── data-management.spec.ts # GDPR compliance tests
│   ├── advanced-features.spec.ts # Advanced functionality
│   └── collaboration.spec.ts # Team collaboration tests
├── utils/
│   ├── helpers.ts           # Test utilities
│   └── selectors.ts         # Common selectors
└── playwright.config.ts     # Playwright configuration
```

### Test Categories

#### 1. Security Tests (`security.spec.ts`)
- **XSS Prevention**: Tests against script injection in all user inputs
- **SQL Injection**: Validates parameterized queries and input sanitization
- **CSRF Protection**: Ensures state-changing operations require valid tokens
- **Authentication**: Password complexity, session management, rate limiting
- **Authorization**: Access control and permission validation
- **Security Headers**: CSP, HSTS, X-Frame-Options validation

#### 2. User Profile Management (`user-profile.spec.ts`)
- **Profile Viewing**: Display of user information and statistics
- **Profile Editing**: Update personal information and preferences
- **Account Settings**: Password changes, 2FA, email preferences
- **Privacy Settings**: Profile visibility and data management
- **Display Settings**: Theme, language, and format preferences
- **Session Management**: Active sessions and security controls

#### 3. Cross-Service Integration (`integration.spec.ts`)
- **Frontend-Backend**: WebSocket updates, data consistency
- **Backend-ML**: Segmentation pipeline, error handling
- **Backend-Database**: Transaction integrity, concurrent operations
- **Full Stack**: Complete user workflows across all services
- **Service Health**: Monitoring and degradation handling

#### 4. Error Recovery and Resilience (`resilience.spec.ts`)
- **Network Errors**: Offline handling, connection recovery
- **Service Failures**: API errors, database issues, ML service outages
- **Data Consistency**: Partial failures, interrupted operations
- **Session Recovery**: Expiration handling, token refresh
- **Browser Resilience**: Navigation, refresh, multi-tab support
- **Performance Degradation**: Memory pressure, high load scenarios

#### 5. Data Management and GDPR (`data-management.spec.ts`)
- **Data Export**: Full account export, selective export, formats
- **Data Deletion**: Right to erasure, cascade deletion
- **Data Access**: Transparency dashboard, activity logs
- **Data Rectification**: Correction capabilities, audit trails
- **Consent Management**: Granular controls, withdrawal options
- **Third-party Controls**: Integration management, data sharing

#### 6. Advanced Features (`advanced-features.spec.ts`)
- **Batch Operations**: Upload, segmentation, export, deletion
- **Advanced Search**: Complex queries, filters, saved searches
- **Export Features**: Custom templates, multiple formats, scheduling
- **Keyboard Shortcuts**: Global and context-specific shortcuts
- **Filtering/Sorting**: Multi-criteria filtering, custom sorting

#### 7. Collaboration (`collaboration.spec.ts`)
- **Project Sharing**: User invitations, permission management
- **Real-time Updates**: Presence indicators, live synchronization
- **Comments**: Threaded discussions, mentions, annotations
- **Team Management**: Team creation, permissions, project sharing
- **Activity Tracking**: Feed, notifications, version control

## Running E2E Tests

### Prerequisites
```bash
# Install dependencies
npm install

# Install Playwright browsers
npx playwright install

# Set up test environment
cp .env.test.example .env.test
```

### Running Tests

#### All Tests
```bash
# Run all E2E tests
npm run e2e

# Run in headed mode (see browser)
npm run e2e:headed

# Run in UI mode (interactive)
npm run e2e:ui
```

#### Specific Test Suites
```bash
# Run security tests only
npm run e2e -- security.spec.ts

# Run specific test
npm run e2e -- security.spec.ts -g "XSS Prevention"

# Run tests matching pattern
npm run e2e -- --grep "authentication"
```

#### Different Browsers
```bash
# Chrome only
npm run e2e -- --project=chromium

# Firefox only
npm run e2e -- --project=firefox

# Safari only
npm run e2e -- --project=webkit

# Mobile Chrome
npm run e2e -- --project="Mobile Chrome"
```

### Debugging Tests
```bash
# Debug mode (opens browser DevTools)
npm run e2e:debug

# Generate trace for debugging
npm run e2e -- --trace on

# Take screenshots on failure
npm run e2e -- --screenshot only-on-failure

# Record videos
npm run e2e -- --video on
```

## Writing New E2E Tests

### Test Structure Template
```typescript
import { test, expect } from '@playwright/test';

test.describe('Feature Name', () => {
  test.beforeEach(async ({ page }) => {
    // Common setup
    await page.goto('/');
    // Login if needed
  });

  test('should do something specific', async ({ page }) => {
    // Arrange
    await page.click('selector');
    
    // Act
    await page.fill('input', 'value');
    await page.click('button');
    
    // Assert
    await expect(page.locator('result')).toBeVisible();
  });
});
```

### Best Practices

1. **Use Data Attributes**: Prefer `data-testid` over CSS selectors
```typescript
// Good
await page.click('[data-testid="submit-button"]');

// Avoid
await page.click('.btn.btn-primary.submit');
```

2. **Wait for Elements**: Use built-in waiting
```typescript
// Good - waits automatically
await expect(page.locator('text=Success')).toBeVisible();

// Avoid manual waits
await page.waitForTimeout(5000);
```

3. **Group Related Tests**: Use describe blocks
```typescript
test.describe('User Authentication', () => {
  test.describe('Login', () => {
    test('valid credentials', async ({ page }) => {});
    test('invalid credentials', async ({ page }) => {});
  });
});
```

4. **Clean Test Data**: Reset state between tests
```typescript
test.afterEach(async ({ page }) => {
  // Clean up created data
  await page.request.delete('/api/test-cleanup');
});
```

5. **Use Page Objects**: For complex pages
```typescript
class LoginPage {
  constructor(private page: Page) {}
  
  async login(email: string, password: string) {
    await this.page.fill('[name="email"]', email);
    await this.page.fill('[name="password"]', password);
    await this.page.click('button[type="submit"]');
  }
}
```

## Coverage Reporting

### Generating Coverage Reports

```bash
# Run tests with coverage collection
npm run e2e:coverage

# Generate HTML report
npm run e2e:coverage:report

# Open coverage report
npm run e2e:coverage:open
```

### Coverage Metrics

We track several types of coverage:

1. **Feature Coverage**: Percentage of user features tested
2. **Interaction Coverage**: User interaction paths covered
3. **Error Coverage**: Error scenarios tested
4. **Integration Coverage**: Cross-service interactions tested
5. **Browser Coverage**: Multi-browser compatibility

### Coverage Goals

- **Critical Paths**: 100% coverage (auth, payments, data integrity)
- **Core Features**: >90% coverage
- **Secondary Features**: >80% coverage
- **Edge Cases**: >70% coverage

## CI/CD Integration

### GitHub Actions Workflow

```yaml
name: E2E Tests

on:
  push:
    branches: [main, dev]
  pull_request:
    branches: [main]

jobs:
  e2e:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npx playwright install --with-deps
      - run: npm run e2e
      - uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
```

### Pre-deployment Checks

1. All E2E tests must pass
2. No decrease in coverage percentage
3. Performance benchmarks met
4. Security tests passing

## Troubleshooting

### Common Issues

1. **Flaky Tests**
   - Add explicit waits for dynamic content
   - Use `toBeVisible()` instead of `toBeAttached()`
   - Increase timeout for slow operations

2. **Authentication Issues**
   - Ensure test user exists
   - Check for rate limiting
   - Verify CSRF token handling

3. **Timeout Errors**
   - Increase global timeout in config
   - Use page.setDefaultTimeout() for specific pages
   - Check for infinite loading states

4. **Selector Issues**
   - Use Playwright Inspector to debug
   - Prefer data-testid attributes
   - Use text selectors for user-visible content

### Debug Commands

```bash
# Open Playwright Inspector
npx playwright test --debug

# Generate codegen
npx playwright codegen localhost:3000

# Show browser logs
npm run e2e -- --reporter=line

# Trace viewer
npx playwright show-trace trace.zip
```

## Maintenance

### Regular Tasks

1. **Weekly**
   - Review flaky tests
   - Update selectors for UI changes
   - Check coverage metrics

2. **Monthly**
   - Add tests for new features
   - Remove tests for deprecated features
   - Update test data

3. **Quarterly**
   - Performance audit
   - Browser compatibility check
   - Security test review

### Test Data Management

1. **Test Users**: Maintained in seed script
2. **Test Projects**: Created and cleaned per test
3. **Test Images**: Stored in `e2e/fixtures/images/`
4. **API Mocks**: Defined in `e2e/mocks/`

## Performance Benchmarks

### Target Metrics

| Test Type | Target Time | Max Time |
|-----------|-------------|----------|
| Unit Test | <100ms | 500ms |
| Integration | <1s | 5s |
| E2E Test | <10s | 30s |
| Full Suite | <10min | 15min |

### Optimization Tips

1. Run tests in parallel
2. Use test caching where appropriate
3. Mock external services
4. Minimize test data setup
5. Reuse authentication state

## Future Improvements

1. **Visual Regression Testing**: Add screenshot comparison
2. **Performance Testing**: Add Lighthouse integration
3. **Accessibility Testing**: Expand WCAG coverage
4. **API Contract Testing**: Add Pact tests
5. **Load Testing**: Add K6 integration
6. **Mobile Testing**: Expand mobile browser coverage

## Resources

- [Playwright Documentation](https://playwright.dev)
- [Testing Best Practices](https://testingjavascript.com)
- [E2E Testing Patterns](https://www.cypress.io/blog/2019/01/03/stop-using-page-objects-and-start-using-app-actions/)
- [OWASP Testing Guide](https://owasp.org/www-project-web-security-testing-guide/)