#!/bin/bash
# Automated Health Monitoring Script
# Runs health checks periodically and sends alerts

set -euo pipefail

# Configuration
CHECK_INTERVAL=300  # 5 minutes
ALERT_THRESHOLD=2   # Number of consecutive failures before alerting
LOG_DIR="/home/<USER>/spheroseg/logs/health-monitor"
STATE_FILE="$LOG_DIR/health-state.json"
SECURE_ENV="/home/<USER>/spheroseg/.env.production.secure"

# Load environment variables
if [ -f "$SECURE_ENV" ]; then
    source "$SECURE_ENV"
fi

# Create log directory
mkdir -p "$LOG_DIR"

# Initialize state file if it doesn't exist
if [ ! -f "$STATE_FILE" ]; then
    echo '{"services":{}}' > "$STATE_FILE"
fi

# Function to send alert
send_alert() {
    local service=$1
    local status=$2
    local message=$3
    local timestamp=$(date -u +%Y-%m-%dT%H:%M:%SZ)
    
    # Log alert
    echo "[$timestamp] ALERT: $service is $status - $message" >> "$LOG_DIR/alerts.log"
    
    # Send email alert (if configured)
    if [ -n "${EMAIL_FROM:-}" ] && [ -n "${EMAIL_HOST:-}" ]; then
        echo -e "Subject: [SpherosegV4 Alert] $service is $status\n\n$message\n\nTimestamp: $timestamp" | \
            sendmail -f "$EMAIL_FROM" -S "$EMAIL_HOST:${EMAIL_PORT:-25}" <EMAIL> 2>/dev/null || true
    fi
    
    # Send to monitoring system (if available)
    if curl -s -f "http://localhost:9093/api/v1/alerts" >/dev/null 2>&1; then
        curl -s -X POST "http://localhost:9093/api/v1/alerts" \
            -H "Content-Type: application/json" \
            -d "[{
                \"labels\": {
                    \"alertname\": \"ServiceHealthCheck\",
                    \"service\": \"$service\",
                    \"severity\": \"$([ "$status" = "unhealthy" ] && echo "critical" || echo "warning")\"
                },
                \"annotations\": {
                    \"description\": \"$message\"
                },
                \"startsAt\": \"$timestamp\"
            }]" 2>/dev/null || true
    fi
}

# Function to update service state
update_state() {
    local service=$1
    local healthy=$2
    local timestamp=$(date -u +%Y-%m-%dT%H:%M:%SZ)
    
    # Read current state
    local current_state=$(jq -r ".services.\"$service\".consecutive_failures // 0" "$STATE_FILE")
    local last_status=$(jq -r ".services.\"$service\".status // \"unknown\"" "$STATE_FILE")
    
    if [ "$healthy" = "true" ]; then
        # Service is healthy
        if [ "$last_status" = "unhealthy" ]; then
            send_alert "$service" "recovered" "Service has recovered and is now healthy"
        fi
        
        # Update state
        jq ".services.\"$service\" = {
            \"status\": \"healthy\",
            \"last_check\": \"$timestamp\",
            \"consecutive_failures\": 0
        }" "$STATE_FILE" > "$STATE_FILE.tmp" && mv "$STATE_FILE.tmp" "$STATE_FILE"
    else
        # Service is unhealthy
        local new_failures=$((current_state + 1))
        
        # Send alert if threshold reached
        if [ "$new_failures" -eq "$ALERT_THRESHOLD" ]; then
            send_alert "$service" "unhealthy" "Service has failed $ALERT_THRESHOLD consecutive health checks"
        fi
        
        # Update state
        jq ".services.\"$service\" = {
            \"status\": \"unhealthy\",
            \"last_check\": \"$timestamp\",
            \"consecutive_failures\": $new_failures
        }" "$STATE_FILE" > "$STATE_FILE.tmp" && mv "$STATE_FILE.tmp" "$STATE_FILE"
    fi
}

# Function to check service health
check_health() {
    local service=$1
    
    case "$service" in
        "backend")
            curl -s -f "http://localhost:5001/health" >/dev/null 2>&1
            ;;
        "ml")
            curl -s -f "http://localhost:5002/health" >/dev/null 2>&1
            ;;
        "database")
            docker exec spheroseg-db pg_isready -U spheroseg_prod -d spheroseg >/dev/null 2>&1
            ;;
        "redis")
            docker exec spheroseg-redis redis-cli ping | grep -q "PONG" 2>/dev/null
            ;;
        "rabbitmq")
            docker exec spheroseg-rabbitmq rabbitmq-diagnostics ping >/dev/null 2>&1
            ;;
        "frontend")
            curl -s -f "http://localhost/" -o /dev/null 2>&1
            ;;
        *)
            return 1
            ;;
    esac
}

# Main monitoring loop
echo "Starting health monitoring (checking every $CHECK_INTERVAL seconds)..."
echo "Alerts will be sent after $ALERT_THRESHOLD consecutive failures"
echo "Logs: $LOG_DIR"
echo "Press Ctrl+C to stop"

while true; do
    TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Check each service
    SERVICES=("backend" "ml" "database" "redis" "rabbitmq" "frontend")
    
    for service in "${SERVICES[@]}"; do
        if check_health "$service"; then
            update_state "$service" "true"
            echo "[$TIMESTAMP] $service: healthy"
        else
            update_state "$service" "false"
            echo "[$TIMESTAMP] $service: UNHEALTHY"
        fi
    done
    
    # Log summary
    HEALTHY_COUNT=$(jq '[.services[] | select(.status == "healthy")] | length' "$STATE_FILE")
    TOTAL_COUNT=$(jq '.services | length' "$STATE_FILE")
    echo "[$TIMESTAMP] Summary: $HEALTHY_COUNT/$TOTAL_COUNT services healthy"
    echo ""
    
    # Wait for next check
    sleep "$CHECK_INTERVAL"
done