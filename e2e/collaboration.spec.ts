import { test, expect } from '@playwright/test';
import { test as baseTest } from './fixtures/base';

// Collaboration and Sharing E2E Tests
test.describe('Collaboration and Sharing', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/auth/signin');
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'testuser123');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
  });

  // Project Sharing Tests
  test.describe('Project Sharing', () => {
    test('should share project with specific user', async ({ page }) => {
      // Navigate to project
      await page.click('.project-card').first();
      
      // Open sharing settings
      await page.click('[data-testid="project-settings"]');
      await page.click('text=Sharing & Permissions');
      
      // Add collaborator
      await expect(page.locator('text=Share Project')).toBeVisible();
      await page.fill('[name="collaboratorEmail"]', '<EMAIL>');
      await page.selectOption('[name="permission"]', 'edit');
      
      // Send invitation
      await page.click('button:has-text("Send Invitation")');
      
      // Should show pending invitation
      await expect(page.locator('text=Invitation <NAME_EMAIL>')).toBeVisible();
      await expect(page.locator('[data-testid="pending-invitation"]')).toBeVisible();
      await expect(page.locator('text=Edit access')).toBeVisible();
    });

    test('should manage sharing permissions', async ({ page }) => {
      await page.click('.project-card').first();
      await page.click('[data-testid="project-settings"]');
      await page.click('text=Sharing & Permissions');
      
      // Test different permission levels
      const permissions = [
        { level: 'view', description: 'Can view project and images' },
        { level: 'comment', description: 'Can view and add comments' },
        { level: 'edit', description: 'Can edit project content' },
        { level: 'admin', description: 'Full project control' }
      ];
      
      for (const perm of permissions) {
        await page.selectOption('[name="defaultPermission"]', perm.level);
        await expect(page.locator(`text=${perm.description}`)).toBeVisible();
      }
    });

    test('should create shareable link', async ({ page }) => {
      await page.click('.project-card').first();
      await page.click('[data-testid="project-settings"]');
      await page.click('text=Sharing & Permissions');
      
      // Generate shareable link
      await page.click('button:has-text("Create Shareable Link")');
      
      // Configure link settings
      await expect(page.locator('text=Link Settings')).toBeVisible();
      await page.selectOption('[name="linkPermission"]', 'view');
      await page.check('[name="linkExpires"]');
      await page.selectOption('[name="linkExpiration"]', '7days');
      await page.check('[name="linkPasswordProtected"]');
      await page.fill('[name="linkPassword"]', 'SecurePass123');
      
      // Create link
      await page.click('button:has-text("Generate Link")');
      
      // Should show link
      const linkElement = page.locator('[data-testid="shareable-link"]');
      await expect(linkElement).toBeVisible();
      const link = await linkElement.inputValue();
      expect(link).toMatch(/https:\/\/.*\/shared\/.*/);
      
      // Copy link
      await page.click('button:has-text("Copy Link")');
      await expect(page.locator('text=Link copied to clipboard')).toBeVisible();
    });

    test('should revoke sharing access', async ({ page }) => {
      await page.click('.project-card').first();
      await page.click('[data-testid="project-settings"]');
      await page.click('text=Sharing & Permissions');
      
      // Find existing collaborator
      const collaborators = page.locator('[data-testid="collaborator-item"]');
      const count = await collaborators.count();
      
      if (count > 0) {
        // Revoke access
        await page.click('[data-testid="revoke-access"]').first();
        
        // Confirm
        await expect(page.locator('text=Remove collaborator?')).toBeVisible();
        await page.click('button:has-text("Remove")');
        
        await expect(page.locator('text=Access revoked')).toBeVisible();
      }
    });
  });

  // Real-time Collaboration Tests
  test.describe('Real-time Collaboration', () => {
    test('should show active collaborators', async ({ page, context }) => {
      // Open project in two tabs
      await page.click('.project-card').first();
      const projectUrl = page.url();
      
      // Open second tab
      const page2 = await context.newPage();
      await page2.goto(projectUrl);
      
      // Should show active users
      await expect(page.locator('[data-testid="active-users"]')).toBeVisible();
      await expect(page.locator('[data-testid="user-avatar"]')).toHaveCount(2);
      
      // Hover for details
      await page.hover('[data-testid="user-avatar"]').first();
      await expect(page.locator('[data-testid="user-tooltip"]')).toBeVisible();
      
      // Clean up
      await page2.close();
    });

    test('should sync edits in real-time', async ({ page, context }) => {
      // Open project in two tabs
      await page.click('.project-card').first();
      const projectUrl = page.url();
      
      const page2 = await context.newPage();
      await page2.goto(projectUrl);
      
      // Edit in first tab
      await page.click('[data-testid="edit-project-name"]');
      await page.fill('[name="projectName"]', 'Collaborative Project Updated');
      await page.click('button:has-text("Save")');
      
      // Should appear in second tab
      await expect(page2.locator('text=Collaborative Project Updated')).toBeVisible({ timeout: 5000 });
      
      // Clean up
      await page2.close();
    });

    test('should show presence indicators', async ({ page, context }) => {
      await page.click('.project-card').first();
      await page.click('[data-testid="image-card"]').first();
      
      // Open same image in second tab
      const imageUrl = page.url();
      const page2 = await context.newPage();
      await page2.goto(imageUrl);
      
      // Should show viewing indicator
      await expect(page.locator('[data-testid="viewing-indicator"]')).toBeVisible();
      await expect(page.locator('text=Also viewing')).toBeVisible();
      
      // Start editing in first tab
      await page.click('button:has-text("Edit Annotations")');
      
      // Should show editing indicator in second tab
      await expect(page2.locator('[data-testid="editing-indicator"]')).toBeVisible();
      await expect(page2.locator('text=is editing')).toBeVisible();
      
      // Clean up
      await page2.close();
    });
  });

  // Comments and Annotations Tests
  test.describe('Comments and Annotations', () => {
    test('should add comments to images', async ({ page }) => {
      await page.click('.project-card').first();
      await page.click('[data-testid="image-card"]').first();
      
      // Open comments panel
      await page.click('button:has-text("Comments")');
      
      // Add comment
      await page.fill('[name="comment"]', 'This cell shows interesting morphology');
      await page.click('button:has-text("Post Comment")');
      
      // Should appear in comments
      await expect(page.locator('text=This cell shows interesting morphology')).toBeVisible();
      await expect(page.locator('[data-testid="comment-author"]')).toContainText('testuser');
      await expect(page.locator('[data-testid="comment-timestamp"]')).toBeVisible();
    });

    test('should support threaded discussions', async ({ page }) => {
      await page.click('.project-card').first();
      await page.click('[data-testid="image-card"]').first();
      await page.click('button:has-text("Comments")');
      
      // Reply to existing comment
      const comments = page.locator('[data-testid="comment-item"]');
      if (await comments.count() > 0) {
        await page.click('[data-testid="reply-button"]').first();
        await page.fill('[name="reply"]', 'I agree, very interesting pattern');
        await page.click('button:has-text("Post Reply")');
        
        // Should show threaded
        await expect(page.locator('[data-testid="comment-thread"]')).toBeVisible();
        await expect(page.locator('text=I agree, very interesting pattern')).toBeVisible();
      }
    });

    test('should add annotations with comments', async ({ page }) => {
      await page.click('.project-card').first();
      await page.click('[data-testid="image-card"]').first();
      
      // Enable annotation mode
      await page.click('button:has-text("Annotate")');
      
      // Draw annotation
      const canvas = page.locator('[data-testid="annotation-canvas"]');
      await canvas.click({ position: { x: 100, y: 100 } });
      await canvas.click({ position: { x: 200, y: 200 } });
      
      // Add annotation comment
      await expect(page.locator('[data-testid="annotation-popup"]')).toBeVisible();
      await page.fill('[name="annotationText"]', 'Unusual cell structure here');
      await page.selectOption('[name="annotationType"]', 'observation');
      await page.click('button:has-text("Save Annotation")');
      
      // Should be saved
      await expect(page.locator('[data-testid="annotation-marker"]')).toBeVisible();
      
      // Hover to see comment
      await page.hover('[data-testid="annotation-marker"]');
      await expect(page.locator('text=Unusual cell structure here')).toBeVisible();
    });

    test('should mention users in comments', async ({ page }) => {
      await page.click('.project-card').first();
      await page.click('[data-testid="image-card"]').first();
      await page.click('button:has-text("Comments")');
      
      // Type mention
      await page.fill('[name="comment"]', 'Hey @');
      
      // Should show user suggestions
      await expect(page.locator('[data-testid="mention-suggestions"]')).toBeVisible();
      
      // Select user
      await page.click('[data-testid="mention-user"]').first();
      
      // Complete comment
      await page.type('[name="comment"]', ' please review this');
      await page.click('button:has-text("Post Comment")');
      
      // Should show mention
      await expect(page.locator('[data-testid="mention-link"]')).toBeVisible();
    });
  });

  // Team Management Tests
  test.describe('Team Management', () => {
    test('should create project teams', async ({ page }) => {
      await page.goto('/teams');
      
      // Create new team
      await page.click('button:has-text("Create Team")');
      
      // Fill team details
      await page.fill('[name="teamName"]', 'Research Team Alpha');
      await page.fill('[name="description"]', 'Cell analysis research group');
      await page.selectOption('[name="visibility"]', 'private');
      
      // Add members
      await page.fill('[name="memberEmail"]', '<EMAIL>');
      await page.click('button:has-text("Add")');
      await page.fill('[name="memberEmail"]', '<EMAIL>');
      await page.click('button:has-text("Add")');
      
      // Create team
      await page.click('button:has-text("Create Team")');
      
      // Should show team created
      await expect(page.locator('text=Team created successfully')).toBeVisible();
      await expect(page.locator('text=Research Team Alpha')).toBeVisible();
    });

    test('should manage team permissions', async ({ page }) => {
      await page.goto('/teams');
      
      // Click on existing team
      const teams = page.locator('[data-testid="team-card"]');
      if (await teams.count() > 0) {
        await teams.first().click();
        
        // Go to permissions
        await page.click('text=Team Settings');
        await page.click('text=Permissions');
        
        // Set team-wide permissions
        await page.check('[name="teamCanCreateProjects"]');
        await page.check('[name="teamCanInviteMembers"]');
        await page.uncheck('[name="teamCanDeleteProjects"]');
        
        // Set member roles
        const members = page.locator('[data-testid="team-member"]');
        if (await members.count() > 0) {
          await page.selectOption(page.locator('[data-testid="member-role"]').first(), 'admin');
        }
        
        // Save
        await page.click('button:has-text("Save Permissions")');
        await expect(page.locator('text=Permissions updated')).toBeVisible();
      }
    });

    test('should share projects with teams', async ({ page }) => {
      await page.click('.project-card').first();
      await page.click('[data-testid="project-settings"]');
      await page.click('text=Sharing & Permissions');
      
      // Switch to team sharing
      await page.click('text=Share with Team');
      
      // Select team
      await page.selectOption('[name="teamId"]', { index: 1 });
      await page.selectOption('[name="teamPermission"]', 'edit');
      
      // Share
      await page.click('button:has-text("Share with Team")');
      
      await expect(page.locator('text=Project shared with team')).toBeVisible();
    });
  });

  // Activity and Notifications Tests
  test.describe('Activity and Notifications', () => {
    test('should show project activity feed', async ({ page }) => {
      await page.click('.project-card').first();
      await page.click('text=Activity');
      
      // Should show activity feed
      await expect(page.locator('[data-testid="activity-feed"]')).toBeVisible();
      await expect(page.locator('[data-testid="activity-item"]').first()).toBeVisible();
      
      // Should show different activity types
      const activityTypes = ['created', 'edited', 'commented', 'shared'];
      for (const type of activityTypes) {
        const activity = page.locator(`[data-activity-type="${type}"]`);
        if (await activity.count() > 0) {
          await expect(activity.first()).toBeVisible();
        }
      }
      
      // Should be filterable
      await page.selectOption('[name="activityFilter"]', 'comments');
      await expect(page.locator('[data-activity-type="commented"]')).toBeVisible();
    });

    test('should send collaboration notifications', async ({ page }) => {
      // Check notifications
      await page.click('[data-testid="notifications-bell"]');
      
      // Should show notification panel
      await expect(page.locator('[data-testid="notifications-panel"]')).toBeVisible();
      
      // Check for collaboration notifications
      const notifications = page.locator('[data-testid="notification-item"]');
      const count = await notifications.count();
      
      if (count > 0) {
        // Should have different types
        await expect(page.locator('text=/shared a project|mentioned you|invited you/')).toBeVisible();
        
        // Click notification
        await notifications.first().click();
        
        // Should navigate to relevant content
        await expect(page.url()).toMatch(/projects|comments|teams/);
      }
    });

    test('should configure notification preferences', async ({ page }) => {
      await page.goto('/profile/settings');
      await page.click('text=Notifications');
      
      // Configure collaboration notifications
      await page.click('text=Collaboration');
      
      await page.check('[name="notifyProjectShared"]');
      await page.check('[name="notifyMentioned"]');
      await page.check('[name="notifyCommented"]');
      await page.uncheck('[name="notifyViewed"]');
      
      // Set delivery preferences
      await page.selectOption('[name="notificationDelivery"]', 'email-digest');
      await page.selectOption('[name="digestFrequency"]', 'daily');
      
      // Save
      await page.click('button:has-text("Save Notification Settings")');
      await expect(page.locator('text=Notification preferences updated')).toBeVisible();
    });
  });

  // Version Control Tests
  test.describe('Version Control', () => {
    test('should track project versions', async ({ page }) => {
      await page.click('.project-card').first();
      await page.click('[data-testid="project-settings"]');
      await page.click('text=Version History');
      
      // Should show version history
      await expect(page.locator('[data-testid="version-timeline"]')).toBeVisible();
      await expect(page.locator('[data-testid="version-item"]').first()).toBeVisible();
      
      // Should show version details
      await page.click('[data-testid="version-item"]').first();
      await expect(page.locator('text=Changes in this version:')).toBeVisible();
      await expect(page.locator('[data-testid="version-diff"]')).toBeVisible();
    });

    test('should create project snapshots', async ({ page }) => {
      await page.click('.project-card').first();
      await page.click('[data-testid="project-menu"]');
      await page.click('text=Create Snapshot');
      
      // Configure snapshot
      await expect(page.locator('text=Create Project Snapshot')).toBeVisible();
      await page.fill('[name="snapshotName"]', 'Pre-publication snapshot');
      await page.fill('[name="snapshotDescription"]', 'State before paper submission');
      await page.check('[name="includeSegmentations"]');
      await page.check('[name="includeComments"]');
      
      // Create
      await page.click('button:has-text("Create Snapshot")');
      
      // Should show progress
      await expect(page.locator('[data-testid="snapshot-progress"]')).toBeVisible();
      
      // Should complete
      await expect(page.locator('text=Snapshot created successfully')).toBeVisible();
    });

    test('should compare versions', async ({ page }) => {
      await page.click('.project-card').first();
      await page.click('[data-testid="project-settings"]');
      await page.click('text=Version History');
      
      // Select two versions to compare
      const versions = page.locator('[data-testid="version-checkbox"]');
      if (await versions.count() >= 2) {
        await versions.nth(0).check();
        await versions.nth(1).check();
        
        // Compare
        await page.click('button:has-text("Compare Selected")');
        
        // Should show comparison
        await expect(page.locator('text=Version Comparison')).toBeVisible();
        await expect(page.locator('[data-testid="version-diff-viewer"]')).toBeVisible();
        await expect(page.locator('text=Added')).toBeVisible();
        await expect(page.locator('text=Removed')).toBeVisible();
        await expect(page.locator('text=Modified')).toBeVisible();
      }
    });
  });
});