#!/bin/bash

# Secrets Validation Script for SpherosegV4
# This script validates that all required secrets are present and properly formatted

set -euo pipefail

ENV_FILE="${1:-/home/<USER>/spheroseg/.env.production.secure}"
ERRORS=0

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check if secret exists and has minimum length
check_secret() {
    local secret_name=$1
    local min_length=$2
    local description=$3
    
    if grep -q "^${secret_name}=" "${ENV_FILE}"; then
        local secret_value=$(grep "^${secret_name}=" "${ENV_FILE}" | cut -d'=' -f2-)
        local secret_length=${#secret_value}
        
        if [ "$secret_length" -ge "$min_length" ]; then
            echo -e "${GREEN}✓${NC} ${secret_name} - ${description} (length: ${secret_length})"
        else
            echo -e "${RED}✗${NC} ${secret_name} - ${description} (length: ${secret_length}, required: ${min_length})"
            ((ERRORS++))
        fi
    else
        echo -e "${RED}✗${NC} ${secret_name} - ${description} (MISSING)"
        ((ERRORS++))
    fi
}

# Function to check if secret contains only valid characters
check_secret_format() {
    local secret_name=$1
    local pattern=$2
    local description=$3
    
    if grep -q "^${secret_name}=" "${ENV_FILE}"; then
        local secret_value=$(grep "^${secret_name}=" "${ENV_FILE}" | cut -d'=' -f2-)
        
        if [[ $secret_value =~ $pattern ]]; then
            echo -e "${GREEN}✓${NC} ${secret_name} format - ${description}"
        else
            echo -e "${RED}✗${NC} ${secret_name} format - ${description} (invalid format)"
            ((ERRORS++))
        fi
    fi
}

echo "Validating secrets in: ${ENV_FILE}"
echo "================================================"

# Check JWT secrets
check_secret "JWT_SECRET" 64 "JWT signing secret"
check_secret "JWT_REFRESH_SECRET" 64 "JWT refresh token secret"
check_secret_format "JWT_SECRET" '^[A-Za-z0-9+/=]+$' "Base64 encoded"
check_secret_format "JWT_REFRESH_SECRET" '^[A-Za-z0-9+/=]+$' "Base64 encoded"

# Check session and CSRF secrets
check_secret "SESSION_SECRET" 32 "Session encryption secret"
check_secret "CSRF_SECRET" 32 "CSRF protection secret"

# Check database credentials
check_secret "DB_PASSWORD" 16 "Database password"
check_secret "POSTGRES_PASSWORD" 16 "PostgreSQL admin password"

# Check RabbitMQ credentials
check_secret "RABBITMQ_PASS" 16 "RabbitMQ password"
check_secret "RABBITMQ_DEFAULT_PASS" 16 "RabbitMQ default password"

# Check Redis password
check_secret "REDIS_PASSWORD" 16 "Redis password"

# Check email configuration (if enabled)
if grep -q "^ENABLE_EMAIL_NOTIFICATIONS=true" "${ENV_FILE}"; then
    echo -e "\n${YELLOW}Email Configuration:${NC}"
    check_secret "EMAIL_USER" 5 "Email username"
    check_secret "EMAIL_PASS" 8 "Email password"
fi

# Check SSL configuration
echo -e "\n${YELLOW}SSL/Security Configuration:${NC}"
if grep -q "^SECURE_COOKIES=true" "${ENV_FILE}"; then
    echo -e "${GREEN}✓${NC} Secure cookies enabled"
else
    echo -e "${YELLOW}⚠${NC} Secure cookies not enabled"
fi

if grep -q "^FORCE_HTTPS=true" "${ENV_FILE}"; then
    echo -e "${GREEN}✓${NC} HTTPS enforcement enabled"
else
    echo -e "${YELLOW}⚠${NC} HTTPS enforcement not enabled"
fi

# Summary
echo "================================================"
if [ "$ERRORS" -eq 0 ]; then
    echo -e "${GREEN}✓ All secrets validated successfully${NC}"
    exit 0
else
    echo -e "${RED}✗ Found $ERRORS validation errors${NC}"
    echo -e "${YELLOW}Run './rotate-secrets.sh' to fix missing or invalid secrets${NC}"
    exit 1
fi