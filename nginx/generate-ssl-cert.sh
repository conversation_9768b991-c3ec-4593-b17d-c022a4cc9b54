#!/bin/bash
# SSL Certificate Generation Script for SpherosegV4

set -e

DOMAIN="spherosegapp.utia.cas.cz"
EMAIL="<EMAIL>"
CERT_DIR="/etc/letsencrypt/live/$DOMAIN"

echo "Generating SSL certificates for $DOMAIN..."

# Check if running in Docker
if [ -f /.dockerenv ]; then
    echo "Running in Docker environment"
    DOCKER_PREFIX="docker-compose exec nginx"
else
    echo "Running on host system"
    DOCKER_PREFIX=""
fi

# Install certbot if not present
if ! command -v certbot &> /dev/null; then
    echo "Installing certbot..."
    if [ -f /etc/debian_version ]; then
        apt-get update && apt-get install -y certbot
    elif [ -f /etc/redhat-release ]; then
        yum install -y certbot
    else
        echo "Please install certbot manually"
        exit 1
    fi
fi

# Create webroot directory for ACME challenge
mkdir -p /var/www/certbot

# Generate certificate
echo "Requesting certificate from Let's Encrypt..."
certbot certonly \
    --webroot \
    --webroot-path=/var/www/certbot \
    --email $EMAIL \
    --agree-tos \
    --no-eff-email \
    --domains $DOMAIN \
    --non-interactive

# Check if certificate was generated
if [ -d "$CERT_DIR" ]; then
    echo "Certificate generated successfully!"
    echo "Certificate location: $CERT_DIR"
    
    # Set proper permissions
    chmod 755 $CERT_DIR
    chmod 644 $CERT_DIR/fullchain.pem
    chmod 600 $CERT_DIR/privkey.pem
    
    echo "SSL certificate setup complete!"
else
    echo "Certificate generation failed!"
    exit 1
fi

# Setup auto-renewal
echo "Setting up auto-renewal..."
(crontab -l 2>/dev/null | grep -v certbot || true; echo "0 0,12 * * * certbot renew --quiet --post-hook 'docker-compose restart nginx'") | crontab -

echo "Auto-renewal cron job created"