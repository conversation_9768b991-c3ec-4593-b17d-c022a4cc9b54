import { test, expect } from '@playwright/test';
import { test as baseTest } from './fixtures/base';

// Error Recovery and Resilience E2E Tests
test.describe('Error Recovery and Resilience', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/auth/signin');
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="password"]', 'testuser123');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard');
  });

  // Network Error Handling Tests
  test.describe('Network Error Handling', () => {
    test('should handle network disconnection gracefully', async ({ page, context }) => {
      // Navigate to dashboard
      await page.goto('/dashboard');
      
      // Go offline
      await context.setOffline(true);
      
      // Try to create project
      await page.click('button:has-text("Create New Project")');
      await page.fill('[name="name"]', 'Offline Test Project');
      await page.click('button:has-text("Create Project")');
      
      // Should show offline error
      await expect(page.locator('text=/Network error|You are offline|Connection lost/')).toBeVisible();
      
      // Go back online
      await context.setOffline(false);
      
      // Should show reconnection message
      await expect(page.locator('text=/Reconnected|Back online|Connection restored/')).toBeVisible({ timeout: 10000 });
      
      // Retry should work
      await page.click('button:has-text("Retry")');
      await expect(page.locator('text=Project created successfully')).toBeVisible();
    });

    test('should queue actions while offline', async ({ page, context }) => {
      await page.goto('/dashboard');
      
      // Go offline
      await context.setOffline(true);
      
      // Perform multiple actions
      await page.click('button:has-text("Create New Project")');
      await page.fill('[name="name"]', 'Queued Project 1');
      await page.click('button:has-text("Create Project")');
      
      await page.click('button:has-text("Create New Project")');
      await page.fill('[name="name"]', 'Queued Project 2');
      await page.click('button:has-text("Create Project")');
      
      // Should show queued status
      await expect(page.locator('text=/Queued|Pending|Will retry/')).toBeVisible();
      
      // Go back online
      await context.setOffline(false);
      
      // Should process queued actions
      await expect(page.locator('text=Queued Project 1')).toBeVisible({ timeout: 15000 });
      await expect(page.locator('text=Queued Project 2')).toBeVisible({ timeout: 15000 });
    });

    test('should handle intermittent network issues', async ({ page, context }) => {
      // Simulate flaky network by intercepting requests
      await page.route('**/api/**', async (route, request) => {
        // Randomly fail 30% of requests
        if (Math.random() < 0.3) {
          await route.abort('failed');
        } else {
          await route.continue();
        }
      });
      
      // Try to load projects
      await page.goto('/dashboard');
      
      // Should eventually load with retries
      await expect(page.locator('.project-card').first()).toBeVisible({ timeout: 20000 });
      
      // Clear route
      await page.unroute('**/api/**');
    });

    test('should handle slow network connections', async ({ page, context }) => {
      // Simulate slow 3G
      await page.route('**/api/**', async (route) => {
        await new Promise(resolve => setTimeout(resolve, 2000)); // 2s delay
        await route.continue();
      });
      
      // Navigate to projects
      await page.goto('/projects');
      
      // Should show loading state
      await expect(page.locator('[data-testid="loading-spinner"]')).toBeVisible();
      
      // Should eventually load
      await expect(page.locator('.project-card').first()).toBeVisible({ timeout: 30000 });
      
      // Should not show timeout error for reasonable delays
      await expect(page.locator('text=Request timeout')).not.toBeVisible();
      
      // Clear route
      await page.unroute('**/api/**');
    });
  });

  // Service Failure Recovery Tests
  test.describe('Service Failure Recovery', () => {
    test('should handle API server errors gracefully', async ({ page }) => {
      // Intercept API calls to simulate server errors
      await page.route('**/api/projects', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal server error' })
        });
      });
      
      // Try to load projects
      await page.goto('/projects');
      
      // Should show error message
      await expect(page.locator('text=/Server error|Something went wrong/')).toBeVisible();
      
      // Should offer retry
      await expect(page.locator('button:has-text("Retry")')).toBeVisible();
      
      // Fix the route
      await page.unroute('**/api/projects');
      
      // Retry should work
      await page.click('button:has-text("Retry")');
      await expect(page.locator('.project-card').first()).toBeVisible();
    });

    test('should handle database connection errors', async ({ page }) => {
      // Simulate database error
      await page.route('**/api/health', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            status: 'degraded',
            services: {
              database: { status: 'down', error: 'Connection refused' },
              redis: { status: 'up' },
              ml: { status: 'up' }
            }
          })
        });
      });
      
      // Check health endpoint
      await page.goto('/dashboard');
      
      // Should show degraded status
      await expect(page.locator('[data-testid="service-status-warning"]')).toBeVisible();
      await expect(page.locator('text=/Database unavailable|Database connection error/')).toBeVisible();
      
      // Core read operations should still work from cache
      await expect(page.locator('.project-card')).toBeVisible();
    });

    test('should handle ML service failures', async ({ page }) => {
      // Navigate to project
      await page.click('.project-card').first();
      
      // Simulate ML service failure
      await page.route('**/api/segmentation/process', route => {
        route.fulfill({
          status: 503,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'ML service unavailable' })
        });
      });
      
      // Try to start segmentation
      await page.click('[data-testid="image-card"]').first();
      await page.click('button:has-text("Start Segmentation")');
      
      // Should show service unavailable error
      await expect(page.locator('text=/ML service unavailable|Processing service is down/')).toBeVisible();
      
      // Should not crash the application
      await expect(page.locator('[data-testid="image-card"]')).toBeVisible();
      
      // Clear route
      await page.unroute('**/api/segmentation/process');
    });
  });

  // Data Consistency and Recovery Tests
  test.describe('Data Consistency and Recovery', () => {
    test('should handle partial upload failures', async ({ page }) => {
      await page.click('.project-card').first();
      
      let uploadCount = 0;
      
      // Simulate failure on 3rd upload
      await page.route('**/api/images/upload', async (route) => {
        uploadCount++;
        if (uploadCount === 3) {
          await route.abort('failed');
        } else {
          await route.continue();
        }
      });
      
      // Upload multiple files
      const [fileChooser] = await Promise.all([
        page.waitForEvent('filechooser'),
        page.click('button:has-text("Upload Images")')
      ]);
      
      await fileChooser.setFiles([
        { name: 'img1.jpg', mimeType: 'image/jpeg', buffer: Buffer.from('data1') },
        { name: 'img2.jpg', mimeType: 'image/jpeg', buffer: Buffer.from('data2') },
        { name: 'img3.jpg', mimeType: 'image/jpeg', buffer: Buffer.from('data3') },
        { name: 'img4.jpg', mimeType: 'image/jpeg', buffer: Buffer.from('data4') }
      ]);
      
      // Should show partial success
      await expect(page.locator('text=/3 of 4 uploaded|Partial upload/')).toBeVisible();
      
      // Should offer to retry failed uploads
      await expect(page.locator('button:has-text("Retry Failed")')).toBeVisible();
      
      // Clear route
      await page.unroute('**/api/images/upload');
    });

    test('should recover from interrupted operations', async ({ page, context }) => {
      // Start a long operation
      await page.click('.project-card').first();
      await page.click('button:has-text("Export")');
      await page.click('text=Export All Data');
      
      // Should show progress
      await expect(page.locator('[data-testid="export-progress"]')).toBeVisible();
      
      // Simulate connection loss mid-operation
      await context.setOffline(true);
      await page.waitForTimeout(1000);
      await context.setOffline(false);
      
      // Should either complete or allow resume
      const completed = await page.locator('text=Export completed').isVisible({ timeout: 5000 }).catch(() => false);
      const resumable = await page.locator('button:has-text("Resume Export")').isVisible({ timeout: 5000 }).catch(() => false);
      
      expect(completed || resumable).toBeTruthy();
    });

    test('should maintain form data during errors', async ({ page }) => {
      // Fill out complex form
      await page.click('button:has-text("Create New Project")');
      const formData = {
        name: 'Complex Project with Long Name',
        description: 'This is a very detailed description with lots of important information that should not be lost',
        tags: 'research, biology, cancer, analysis',
        isPublic: true
      };
      
      await page.fill('[name="name"]', formData.name);
      await page.fill('[name="description"]', formData.description);
      await page.fill('[name="tags"]', formData.tags);
      await page.check('[name="isPublic"]');
      
      // Simulate API error
      await page.route('**/api/projects', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Server error' })
        });
      });
      
      // Submit form
      await page.click('button:has-text("Create Project")');
      
      // Should show error
      await expect(page.locator('text=/Error|Failed/')).toBeVisible();
      
      // Form data should be preserved
      await expect(page.locator('[name="name"]')).toHaveValue(formData.name);
      await expect(page.locator('[name="description"]')).toHaveValue(formData.description);
      await expect(page.locator('[name="tags"]')).toHaveValue(formData.tags);
      await expect(page.locator('[name="isPublic"]')).toBeChecked();
      
      // Clear route
      await page.unroute('**/api/projects');
    });
  });

  // Session Recovery Tests
  test.describe('Session Recovery', () => {
    test('should handle session expiration gracefully', async ({ page, context }) => {
      // Navigate to protected page
      await page.goto('/projects');
      
      // Simulate session expiration by clearing cookies
      await context.clearCookies();
      
      // Try to perform action
      await page.click('button:has-text("Create New Project")').catch(() => {});
      
      // Should redirect to login with message
      await expect(page).toHaveURL(/.*signin/);
      await expect(page.locator('text=/Session expired|Please login again/')).toBeVisible();
      
      // Should preserve intended destination
      await page.fill('[name="email"]', '<EMAIL>');
      await page.fill('[name="password"]', 'testuser123');
      await page.click('button[type="submit"]');
      
      // Should return to projects page
      await expect(page).toHaveURL(/.*projects/);
    });

    test('should refresh authentication tokens automatically', async ({ page }) => {
      // Monitor network requests
      let tokenRefreshed = false;
      
      page.on('response', response => {
        if (response.url().includes('/auth/refresh') && response.status() === 200) {
          tokenRefreshed = true;
        }
      });
      
      // Simulate long session by waiting
      await page.goto('/dashboard');
      
      // Perform actions over time
      for (let i = 0; i < 3; i++) {
        await page.waitForTimeout(5000);
        await page.reload();
      }
      
      // Token refresh should have occurred
      expect(tokenRefreshed).toBeTruthy();
    });
  });

  // Browser-Specific Resilience Tests
  test.describe('Browser Resilience', () => {
    test('should handle browser back/forward navigation', async ({ page }) => {
      // Navigate through multiple pages
      await page.goto('/dashboard');
      await page.click('.project-card').first();
      const projectUrl = page.url();
      
      await page.click('[data-testid="image-card"]').first();
      const imageUrl = page.url();
      
      // Go back
      await page.goBack();
      await expect(page).toHaveURL(projectUrl);
      await expect(page.locator('[data-testid="project-header"]')).toBeVisible();
      
      // Go forward
      await page.goForward();
      await expect(page).toHaveURL(imageUrl);
      await expect(page.locator('[data-testid="image-details"]')).toBeVisible();
      
      // Data should be preserved
      await expect(page.locator('[data-testid="image-metadata"]')).toBeVisible();
    });

    test('should handle page refresh without data loss', async ({ page }) => {
      // Navigate to form
      await page.click('button:has-text("Create New Project")');
      
      // Fill form partially
      await page.fill('[name="name"]', 'Refresh Test Project');
      await page.fill('[name="description"]', 'Testing refresh resilience');
      
      // Save to local storage (if implemented)
      await page.waitForTimeout(1000); // Allow auto-save
      
      // Refresh page
      await page.reload();
      
      // Check if form data is restored (depends on implementation)
      const nameValue = await page.locator('[name="name"]').inputValue();
      const hasAutoSave = nameValue === 'Refresh Test Project';
      
      if (hasAutoSave) {
        expect(nameValue).toBe('Refresh Test Project');
        await expect(page.locator('text=Draft restored')).toBeVisible();
      }
    });

    test('should handle multiple tabs/windows', async ({ page, context }) => {
      // Open second tab
      const page2 = await context.newPage();
      await page2.goto('/dashboard');
      
      // Make change in first tab
      await page.click('button:has-text("Create New Project")');
      await page.fill('[name="name"]', 'Multi-Tab Project');
      await page.click('button:has-text("Create Project")');
      
      // Second tab should update (via WebSocket or polling)
      await expect(page2.locator('text=Multi-Tab Project')).toBeVisible({ timeout: 10000 });
      
      // Clean up
      await page2.close();
    });
  });

  // Performance Degradation Handling Tests
  test.describe('Performance Degradation Handling', () => {
    test('should handle memory pressure gracefully', async ({ page }) => {
      // Navigate to image gallery with many images
      await page.click('.project-card').first();
      
      // Scroll through many images to trigger potential memory issues
      for (let i = 0; i < 20; i++) {
        await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
        await page.waitForTimeout(500);
      }
      
      // App should still be responsive
      await page.click('button:has-text("Upload Images")');
      await expect(page.locator('[data-testid="upload-dialog"]')).toBeVisible();
      
      // Should implement virtual scrolling or pagination
      const images = await page.locator('[data-testid="image-card"]').count();
      const viewport = await page.locator('[data-testid="image-grid"]').boundingBox();
      
      // Not all images should be in DOM if virtual scrolling is implemented
      expect(images).toBeLessThan(100); // Assuming there are many images
    });

    test('should degrade features under high load', async ({ page }) => {
      // Simulate high load by opening multiple operations
      const promises = [];
      
      // Start multiple exports
      for (let i = 0; i < 5; i++) {
        promises.push(
          page.request.post('/api/export', {
            data: { projectId: 1, format: 'json' }
          })
        );
      }
      
      // Check if rate limiting or queueing is applied
      const results = await Promise.allSettled(promises);
      const rateLimited = results.filter(r => 
        r.status === 'fulfilled' && r.value.status() === 429
      ).length;
      
      // Some requests should be rate limited
      expect(rateLimited).toBeGreaterThan(0);
      
      // UI should show appropriate message
      await page.goto('/dashboard');
      if (rateLimited > 0) {
        await expect(page.locator('text=/Rate limit|Too many requests|Please slow down/')).toBeVisible();
      }
    });
  });
});