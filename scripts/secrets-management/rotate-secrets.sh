#!/bin/bash

# Secrets Rotation Script for SpherosegV4
# This script rotates critical secrets in production environment

set -euo pipefail

# Configuration
SECRETS_DIR="/home/<USER>/spheroseg/secrets"
BACKUP_DIR="${SECRETS_DIR}/backups/$(date +%Y%m%d_%H%M%S)"
ENV_FILE="/home/<USER>/spheroseg/.env.production.secure"

# Create directories
mkdir -p "${SECRETS_DIR}" "${BACKUP_DIR}"

# Function to generate secure random string
generate_secret() {
    openssl rand -base64 "$1" | tr -d '\n'
}

# Function to rotate a secret
rotate_secret() {
    local secret_name=$1
    local secret_length=$2
    local new_secret=$(generate_secret "$secret_length")
    
    # Backup current secret
    grep "^${secret_name}=" "${ENV_FILE}" >> "${BACKUP_DIR}/secrets.backup" || true
    
    # Update secret in env file
    if grep -q "^${secret_name}=" "${ENV_FILE}"; then
        sed -i "s|^${secret_name}=.*|${secret_name}=${new_secret}|" "${ENV_FILE}"
        echo "✓ Rotated ${secret_name}"
    else
        echo "${secret_name}=${new_secret}" >> "${ENV_FILE}"
        echo "✓ Added ${secret_name}"
    fi
}

# Function to restart services after rotation
restart_services() {
    echo "Restarting services with new secrets..."
    docker-compose --env-file "${ENV_FILE}" --profile prod restart backend
    echo "✓ Services restarted"
}

# Main rotation process
echo "Starting secrets rotation..."
echo "Backup location: ${BACKUP_DIR}"

# Rotate JWT secrets
rotate_secret "JWT_SECRET" 64
rotate_secret "JWT_REFRESH_SECRET" 64

# Rotate session and CSRF secrets
rotate_secret "SESSION_SECRET" 48
rotate_secret "CSRF_SECRET" 48

# Rotate database password (requires additional steps)
echo "Database password rotation requires manual intervention:"
echo "1. Generate new password: $(generate_secret 32)"
echo "2. Update PostgreSQL user password"
echo "3. Update DATABASE_URL and DB_PASSWORD in .env.production.secure"
echo "4. Restart all services"

# Rotate RabbitMQ credentials
echo "RabbitMQ credentials rotation requires manual intervention:"
echo "1. Generate new password: $(generate_secret 24)"
echo "2. Update RabbitMQ user password via management console"
echo "3. Update RABBITMQ_PASS in .env.production.secure"
echo "4. Restart backend and ML services"

# Create rotation log
cat > "${BACKUP_DIR}/rotation.log" <<EOF
Rotation Date: $(date)
Rotated Secrets:
- JWT_SECRET
- JWT_REFRESH_SECRET
- SESSION_SECRET
- CSRF_SECRET

Manual Rotation Required:
- Database password
- RabbitMQ credentials
- Redis password (if enabled)
EOF

echo "✓ Secrets rotation completed"
echo "⚠️  Remember to restart services for changes to take effect"
echo "⚠️  Manual rotation required for database and RabbitMQ credentials"