#!/bin/bash
# Monitoring Stack Deployment Script for SpherosegV4
# Deploys Prometheus, Grafana, Alertmanager, and exporters

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Log functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# Configuration
MONITORING_DIR="/home/<USER>/spheroseg/monitoring"
SECURE_ENV="/home/<USER>/spheroseg/.env.production.secure"

# Change to project directory
cd /home/<USER>/spheroseg

log_info "Starting monitoring stack deployment..."

# Load secure environment variables
if [ -f "$SECURE_ENV" ]; then
    log_info "Loading secure environment variables..."
    source "$SECURE_ENV"
else
    log_error "Secure environment file not found: $SECURE_ENV"
    exit 1
fi

# Ensure monitoring directory exists
if [ ! -d "$MONITORING_DIR" ]; then
    log_error "Monitoring directory not found: $MONITORING_DIR"
    exit 1
fi

# Create external network if it doesn't exist
log_info "Creating Docker network..."
docker network create spheroseg_default 2>/dev/null || log_info "Network spheroseg_default already exists"

# Update Prometheus configuration with secure credentials
log_info "Updating Prometheus configuration with secure credentials..."
cat > "$MONITORING_DIR/prometheus/prometheus.yml" <<EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'spheroseg-monitor'
    environment: 'production'

alerting:
  alertmanagers:
    - static_configs:
        - targets: ['alertmanager:9093']

rule_files:
  - "/etc/prometheus/alerts/*.yml"

scrape_configs:
  # Spheroseg Backend Service
  - job_name: 'spheroseg-backend'
    static_configs:
      - targets: ['backend:5001']
    metrics_path: '/api/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  # ML Service
  - job_name: 'spheroseg-ml'
    static_configs:
      - targets: ['ml:5002']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Node Exporter
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # PostgreSQL Exporter
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Redis Exporter
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  # Nginx
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-prod:9113']
    metrics_path: '/metrics'
EOF

# Create application alerts
log_info "Creating application alerts..."
cat > "$MONITORING_DIR/prometheus/alerts/application.yml" <<'EOF'
groups:
  - name: spheroseg_application
    interval: 30s
    rules:
      # High error rate
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.instance }}"

      # High response time
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s for {{ $labels.instance }}"

      # Database connection errors
      - alert: DatabaseConnectionErrors
        expr: rate(database_connection_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Database connection errors"
          description: "Database connection error rate is {{ $value }} per second"

      # ML Service down
      - alert: MLServiceDown
        expr: up{job="spheroseg-ml"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "ML Service is down"
          description: "ML Service has been down for more than 1 minute"

      # High memory usage
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value | humanizePercentage }}"

      # Disk space low
      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) < 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space"
          description: "Only {{ $value | humanizePercentage }} disk space remaining"
EOF

# Create infrastructure alerts
log_info "Creating infrastructure alerts..."
cat > "$MONITORING_DIR/prometheus/alerts/infrastructure.yml" <<'EOF'
groups:
  - name: spheroseg_infrastructure
    interval: 30s
    rules:
      # Container down
      - alert: ContainerDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Container {{ $labels.job }} is down"
          description: "Container {{ $labels.instance }} has been down for more than 1 minute"

      # High CPU usage
      - alert: HighCPUUsage
        expr: 100 - (avg by (instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% for {{ $labels.instance }}"

      # PostgreSQL down
      - alert: PostgreSQLDown
        expr: pg_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL database has been down for more than 1 minute"

      # Redis down
      - alert: RedisDown
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis is down"
          description: "Redis has been down for more than 1 minute"
EOF

# Update Alertmanager configuration
log_info "Updating Alertmanager configuration..."
cat > "$MONITORING_DIR/alertmanager/config.yml" <<EOF
global:
  resolve_timeout: 5m
  smtp_from: '<EMAIL>'
  smtp_smarthost: 'mail.utia.cas.cz:25'
  smtp_require_tls: false

route:
  group_by: ['alertname', 'severity']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'email-notifications'
  routes:
    - match:
        severity: critical
      receiver: 'critical-notifications'
      continue: true

receivers:
  - name: 'email-notifications'
    email_configs:
      - to: '<EMAIL>'
        headers:
          Subject: 'SpherosegV4 Alert: {{ .GroupLabels.alertname }}'
        html: |
          <h2>{{ .GroupLabels.alertname }}</h2>
          <p><b>Severity:</b> {{ .GroupLabels.severity }}</p>
          {{ range .Alerts }}
          <hr>
          <p><b>Summary:</b> {{ .Annotations.summary }}</p>
          <p><b>Description:</b> {{ .Annotations.description }}</p>
          <p><b>Labels:</b> {{ .Labels }}</p>
          {{ end }}

  - name: 'critical-notifications'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        headers:
          Subject: '[CRITICAL] SpherosegV4 Alert: {{ .GroupLabels.alertname }}'
        send_resolved: true
EOF

# Create Grafana provisioning for datasources
log_info "Creating Grafana datasource provisioning..."
mkdir -p "$MONITORING_DIR/grafana/provisioning/datasources"
cat > "$MONITORING_DIR/grafana/provisioning/datasources/prometheus.yml" <<EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: false
EOF

# Create Grafana provisioning for dashboards
log_info "Creating Grafana dashboard provisioning..."
mkdir -p "$MONITORING_DIR/grafana/provisioning/dashboards"
cat > "$MONITORING_DIR/grafana/provisioning/dashboards/dashboard.yml" <<EOF
apiVersion: 1

providers:
  - name: 'SpherosegV4'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards
EOF

# Update docker-compose with secure environment
log_info "Updating monitoring docker-compose with secure credentials..."
cat > "$MONITORING_DIR/docker-compose.monitoring.yml" <<EOF
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: spheroseg-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./prometheus/alerts:/etc/prometheus/alerts:ro
      - prometheus-data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - spheroseg_default
    restart: unless-stopped

  grafana:
    image: grafana/grafana:10.1.5
    container_name: spheroseg-grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_ROOT_URL=https://spherosegapp.utia.cas.cz/grafana
      - GF_SERVER_SERVE_FROM_SUB_PATH=true
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource,grafana-piechart-panel
    volumes:
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
      - ./grafana/dashboards:/var/lib/grafana/dashboards:ro
      - grafana-data:/var/lib/grafana
    ports:
      - "3001:3000"
    networks:
      - spheroseg_default
    restart: unless-stopped
    depends_on:
      - prometheus

  alertmanager:
    image: prom/alertmanager:v0.26.0
    container_name: spheroseg-alertmanager
    command:
      - '--config.file=/etc/alertmanager/config.yml'
      - '--storage.path=/alertmanager'
    volumes:
      - ./alertmanager/config.yml:/etc/alertmanager/config.yml:ro
      - alertmanager-data:/alertmanager
    ports:
      - "9093:9093"
    networks:
      - spheroseg_default
    restart: unless-stopped

  node-exporter:
    image: prom/node-exporter:v1.6.1
    container_name: spheroseg-node-exporter
    command:
      - '--path.rootfs=/host'
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    ports:
      - "9100:9100"
    networks:
      - spheroseg_default
    restart: unless-stopped
    privileged: true

  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:v0.13.2
    container_name: spheroseg-postgres-exporter
    environment:
      DATA_SOURCE_NAME: "postgresql://${DB_USER}:${DB_PASSWORD}@db:5432/spheroseg?sslmode=disable"
    ports:
      - "9187:9187"
    networks:
      - spheroseg_default
    restart: unless-stopped

  redis-exporter:
    image: oliver006/redis_exporter:v1.54.0
    container_name: spheroseg-redis-exporter
    environment:
      REDIS_ADDR: "redis://redis:6379"
      REDIS_PASSWORD: "${REDIS_PASSWORD}"
    ports:
      - "9121:9121"
    networks:
      - spheroseg_default
    restart: unless-stopped

volumes:
  prometheus-data:
  grafana-data:
  alertmanager-data:

networks:
  spheroseg_default:
    external: true
EOF

# Deploy the monitoring stack
log_info "Deploying monitoring stack..."
cd "$MONITORING_DIR"
docker-compose -f docker-compose.monitoring.yml up -d

# Wait for services to start
log_info "Waiting for services to start..."
sleep 15

# Check service health
log_info "Checking monitoring services health..."
SERVICES=("prometheus" "grafana" "alertmanager" "node-exporter" "postgres-exporter" "redis-exporter")
ALL_HEALTHY=true

for service in "${SERVICES[@]}"; do
    if docker ps | grep -q "spheroseg-$service"; then
        log_info "✓ $service is running"
    else
        log_error "✗ $service is not running"
        ALL_HEALTHY=false
    fi
done

if [ "$ALL_HEALTHY" = true ]; then
    log_info "All monitoring services are running successfully!"
    echo ""
    log_info "Monitoring Stack URLs:"
    echo "  Prometheus: http://localhost:9090"
    echo "  Grafana: http://localhost:3001 (admin/${GRAFANA_ADMIN_PASSWORD})"
    echo "  Alertmanager: http://localhost:9093"
    echo ""
    log_info "Next steps:"
    echo "  1. Configure nginx to proxy Grafana at /grafana"
    echo "  2. Import dashboards in Grafana UI"
    echo "  3. Configure alert notification channels"
    echo "  4. Test alerting rules"
else
    log_error "Some monitoring services failed to start"
    echo "Check logs with: docker-compose -f $MONITORING_DIR/docker-compose.monitoring.yml logs"
    exit 1
fi

log_info "Monitoring deployment completed!"