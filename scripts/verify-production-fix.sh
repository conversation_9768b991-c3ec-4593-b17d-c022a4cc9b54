#!/bin/bash

# Verify production fix for JavaScript initialization errors

echo "🔍 Verifying Production Fix..."
echo "=================================="

# Test local deployment
echo -e "\n📋 Testing Local Deployment (localhost:3000)..."
LOCAL_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000)
echo "HTTP Status: $LOCAL_STATUS"

# Check for JavaScript errors locally
LOCAL_ERRORS=$(curl -s http://localhost:3000 | grep -c "Cannot access.*before initialization")
if [ "$LOCAL_ERRORS" -eq 0 ]; then
    echo "✅ No JavaScript initialization errors found locally"
else
    echo "❌ JavaScript errors still present locally!"
fi

# Test production site
echo -e "\n📋 Testing Production Site (spherosegapp.utia.cas.cz)..."
PROD_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://spherosegapp.utia.cas.cz/)
echo "HTTP Status: $PROD_STATUS"

# Check for JavaScript errors on production
PROD_ERRORS=$(curl -s https://spherosegapp.utia.cas.cz/ | grep -c "Cannot access.*before initialization")
if [ "$PROD_ERRORS" -eq 0 ]; then
    echo "✅ No JavaScript initialization errors found on production"
else
    echo "❌ JavaScript errors may still be present on production!"
fi

# Check vendor file details
echo -e "\n📋 Vendor File Analysis..."
VENDOR_FILES=$(curl -s https://spherosegapp.utia.cas.cz/ | grep -oE 'vendor-[a-zA-Z0-9-]+\.js' | sort | uniq)
echo "Vendor files loaded:"
echo "$VENDOR_FILES" | while read -r file; do
    echo "  - $file"
done

# Check minifier being used
echo -e "\n📋 Build Configuration..."
if [ -f "/home/<USER>/spheroseg/packages/frontend/vite.config.ts" ]; then
    MINIFIER=$(grep -E "minify:" /home/<USER>/spheroseg/packages/frontend/vite.config.ts | grep -oE "'[^']+'" | tr -d "'")
    echo "Minifier: $MINIFIER"
fi

echo -e "\n=================================="
echo "📊 Summary:"
if [ "$LOCAL_ERRORS" -eq 0 ] && [ "$PROD_ERRORS" -eq 0 ]; then
    echo "✅ Fix appears to be working!"
    echo ""
    echo "Next steps:"
    echo "1. Clear browser cache (Ctrl+Shift+R or Cmd+Shift+R)"
    echo "2. Test in incognito/private mode"
    echo "3. Check browser console for any remaining errors"
else
    echo "⚠️  Issues may still be present. Please check browser console."
fi